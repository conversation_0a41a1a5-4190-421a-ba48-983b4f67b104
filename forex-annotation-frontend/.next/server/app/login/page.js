/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/auth-provider.tsx */ \"(ssr)/./src/components/auth/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZwbG90cHJvJTJGTGlicmFyeSUyRkNsb3VkU3RvcmFnZSUyRlN5bm9sb2d5RHJpdmUtUExvVERyaXZlJTJGTXlfTGlmZSUyRlN0b2NrJTIwTWFya2V0JTJGQ2xhdWRlJTIwQ29kZSUyRmZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGYXV0aCUyRmF1dGgtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBbU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kLz9mMGYzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3Bsb3Rwcm8vTGlicmFyeS9DbG91ZFN0b3JhZ2UvU3lub2xvZ3lEcml2ZS1QTG9URHJpdmUvTXlfTGlmZS9TdG9jayBNYXJrZXQvQ2xhdWRlIENvZGUvZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Flogin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Flogin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGc3JjJTJGYXBwJTJGbG9naW4lMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQWtMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8/YjU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9wbG90cHJvL0xpYnJhcnkvQ2xvdWRTdG9yYWdlL1N5bm9sb2d5RHJpdmUtUExvVERyaXZlL015X0xpZmUvU3RvY2sgTWFya2V0L0NsYXVkZSBDb2RlL2ZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQvc3JjL2FwcC9sb2dpbi9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Flogin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_login_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/login-form */ \"(ssr)/./src/components/auth/login-form.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LoginPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"FOREX Annotation Tool\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Professional trading pattern analysis platform\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_login_form__WEBPACK_IMPORTED_MODULE_1__.LoginForm, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvZ2luL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXdEO0FBRXpDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBd0M7Ozs7OztzQ0FHdEQsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7OzhCQUkvQiw4REFBQ0gsa0VBQVNBOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWxCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8uL3NyYy9hcHAvbG9naW4vcGFnZS50c3g/ZmM2MyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgTG9naW5Gb3JtIH0gZnJvbSAnQC9jb21wb25lbnRzL2F1dGgvbG9naW4tZm9ybSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9naW5QYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYXktNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctbWQgdy1mdWxsIHNwYWNlLXktOCBwLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICBGT1JFWCBBbm5vdGF0aW9uIFRvb2xcbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIFByb2Zlc3Npb25hbCB0cmFkaW5nIHBhdHRlcm4gYW5hbHlzaXMgcGxhdGZvcm1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8TG9naW5Gb3JtIC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufSJdLCJuYW1lcyI6WyJMb2dpbkZvcm0iLCJMb2dpblBhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/auth-provider.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/auth-provider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\n\nfunction AuthProvider({ children }) {\n    const initialize = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)((state)=>state.initialize);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize auth state from localStorage on app start\n        initialize();\n    }, [\n        initialize\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFd0M7QUFDVTtBQU0zQyxTQUFTRyxhQUFhLEVBQUVDLFFBQVEsRUFBcUI7SUFDMUQsTUFBTUMsYUFBYUgsZ0VBQVlBLENBQUNJLENBQUFBLFFBQVNBLE1BQU1ELFVBQVU7SUFFekRKLGdEQUFTQSxDQUFDO1FBQ1IsdURBQXVEO1FBQ3ZESTtJQUNGLEdBQUc7UUFBQ0E7S0FBVztJQUVmLHFCQUFPO2tCQUFHRDs7QUFDWiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4P2IzMzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJ0Avc3RvcmVzL2F1dGgtc3RvcmUnXG5cbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IEF1dGhQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IGluaXRpYWxpemUgPSB1c2VBdXRoU3RvcmUoc3RhdGUgPT4gc3RhdGUuaW5pdGlhbGl6ZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXRpYWxpemUgYXV0aCBzdGF0ZSBmcm9tIGxvY2FsU3RvcmFnZSBvbiBhcHAgc3RhcnRcbiAgICBpbml0aWFsaXplKClcbiAgfSwgW2luaXRpYWxpemVdKVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VBdXRoU3RvcmUiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsImluaXRpYWxpemUiLCJzdGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/login-form.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/login-form.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \n\n\n\n\n\n\nfunction LoginForm() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, isLoading } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_6__.useAuthStore)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"\",\n        password: \"\"\n    });\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        try {\n            await login(formData);\n            router.push(\"/dashboard\");\n        } catch (err) {\n            setError(err.response?.data?.detail || \"Login failed. Please try again.\");\n        }\n    };\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"w-full max-w-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                className: \"space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                        className: \"text-2xl text-center\",\n                        children: \"Sign In\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                        className: \"text-center\",\n                        children: \"Enter your credentials to access the FOREX annotation tool\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"username\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Username\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"username\",\n                                        name: \"username\",\n                                        type: \"text\",\n                                        placeholder: \"Enter your username\",\n                                        value: formData.username,\n                                        onChange: handleChange,\n                                        required: true,\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"password\",\n                                        name: \"password\",\n                                        type: \"password\",\n                                        placeholder: \"Enter your password\",\n                                        value: formData.password,\n                                        onChange: handleChange,\n                                        required: true,\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-red-600 bg-red-50 p-3 rounded-md\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                type: \"submit\",\n                                className: \"w-full\",\n                                disabled: isLoading,\n                                children: isLoading ? \"Signing in...\" : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600\",\n                                children: \"Don't have an account? \"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/register\"),\n                                className: \"text-blue-600 hover:text-blue-500 font-medium\",\n                                children: \"Sign up\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/login-form.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/login-form.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            success: \"bg-green-600 text-white hover:bg-green-700\",\n            warning: \"bg-yellow-600 text-white hover:bg-yellow-700\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3g/Yzk4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9Il0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = \"v1\";\nclass ApiClient {\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: `${API_BASE_URL}/api/${API_VERSION}`,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = localStorage.getItem(\"auth_token\");\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            return config;\n        });\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            if (error.response?.status === 401) {\n                // Clear token on 401\n                localStorage.removeItem(\"auth_token\");\n                localStorage.removeItem(\"user\");\n                window.location.href = \"/login\";\n            }\n            return Promise.reject(error);\n        });\n    }\n    // Authentication endpoints\n    async login(credentials) {\n        const formData = new FormData();\n        formData.append(\"username\", credentials.username);\n        formData.append(\"password\", credentials.password);\n        const response = await this.client.post(\"/auth/login\", formData, {\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        return response.data;\n    }\n    async register(data) {\n        const response = await this.client.post(\"/auth/register\", data);\n        return response.data;\n    }\n    async getCurrentUser() {\n        const response = await this.client.get(\"/auth/me\");\n        return response.data;\n    }\n    async getUsers() {\n        const response = await this.client.get(\"/auth/users\");\n        return response.data;\n    }\n    // Project endpoints\n    async getProjects() {\n        const response = await this.client.get(\"/projects\");\n        return response.data;\n    }\n    async createProject(projectData) {\n        const response = await this.client.post(\"/projects\", projectData);\n        return response.data;\n    }\n    async getProject(id) {\n        const response = await this.client.get(`/projects/${id}`);\n        return response.data;\n    }\n    async updateProject(id, data) {\n        const response = await this.client.put(`/projects/${id}`, data);\n        return response.data;\n    }\n    async deleteProject(id) {\n        await this.client.delete(`/projects/${id}`);\n    }\n    async getProjectStats(id) {\n        const response = await this.client.get(`/projects/${id}/stats`);\n        return response.data;\n    }\n    // Volume Profile Wave endpoints\n    async createVolumeWave(data) {\n        const response = await this.client.post(\"/volume-waves\", data);\n        return response.data;\n    }\n    async getVolumeWaves(projectId, includeCompleted = true) {\n        const response = await this.client.get(\"/volume-waves\", {\n            params: {\n                project_id: projectId,\n                include_completed: includeCompleted\n            }\n        });\n        return response.data;\n    }\n    async getVolumeWave(id) {\n        const response = await this.client.get(`/volume-waves/${id}`);\n        return response.data;\n    }\n    async updateWaveCompletion(id, data) {\n        const response = await this.client.put(`/volume-waves/${id}/completion`, data);\n        return response.data;\n    }\n    async getActiveWaves(projectId) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/active`);\n        return response.data;\n    }\n    async getCompletedWaves(projectId, limit = 100) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/completed`, {\n            params: {\n                limit\n            }\n        });\n        return response.data;\n    }\n    async getWaveSuccessRate(projectId, minimumAccuracy = 70) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/success-rate`, {\n            params: {\n                minimum_accuracy: minimumAccuracy\n            }\n        });\n        return response.data;\n    }\n    async exportTrainingData(projectId) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/training-data`);\n        return response.data;\n    }\n    async deleteVolumeWave(id) {\n        await this.client.delete(`/volume-waves/${id}`);\n    }\n    async validateWaveFormula(id) {\n        const response = await this.client.get(`/volume-waves/${id}/formula-validation`);\n        return response.data;\n    }\n    // Annotation endpoints\n    async createAnnotation(data) {\n        const response = await this.client.post(\"/annotations\", data);\n        return response.data;\n    }\n    async getAnnotations(projectId, annotationType, validationStatus) {\n        const params = {\n            project_id: projectId\n        };\n        if (annotationType) params.annotation_type = annotationType;\n        if (validationStatus) params.validation_status = validationStatus;\n        const response = await this.client.get(\"/annotations\", {\n            params\n        });\n        return response.data;\n    }\n    async getAnnotation(id) {\n        const response = await this.client.get(`/annotations/${id}`);\n        return response.data;\n    }\n    async updateAnnotation(id, data) {\n        const response = await this.client.put(`/annotations/${id}`, data);\n        return response.data;\n    }\n    async deleteAnnotation(id) {\n        await this.client.delete(`/annotations/${id}`);\n    }\n    async getAnnotationSummary(projectId) {\n        const response = await this.client.get(`/annotations/projects/${projectId}/summary`);\n        return response.data;\n    }\n    async addPatternMetrics(annotationId, metrics) {\n        const response = await this.client.post(`/annotations/${annotationId}/metrics`, metrics);\n        return response.data;\n    }\n    async getAnnotationMetrics(annotationId) {\n        const response = await this.client.get(`/annotations/${annotationId}/metrics`);\n        return response.data;\n    }\n    // Custom request method for additional flexibility\n    async request(config) {\n        const response = await this.client.request(config);\n        return response.data;\n    }\n}\n// Export singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePercentageChange: () => (/* binding */ calculatePercentageChange),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatVolume: () => (/* binding */ formatVolume),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getPerformanceColor: () => (/* binding */ getPerformanceColor),\n/* harmony export */   getWaveColor: () => (/* binding */ getWaveColor),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format price with appropriate decimal places\nfunction formatPrice(price, decimals = 4) {\n    return price.toFixed(decimals);\n}\n// Format percentage\nfunction formatPercentage(value, decimals = 2) {\n    return `${value.toFixed(decimals)}%`;\n}\n// Format volume with K/M/B suffixes\nfunction formatVolume(volume) {\n    if (volume >= 1e9) {\n        return `${(volume / 1e9).toFixed(1)}B`;\n    } else if (volume >= 1e6) {\n        return `${(volume / 1e6).toFixed(1)}M`;\n    } else if (volume >= 1e3) {\n        return `${(volume / 1e3).toFixed(1)}K`;\n    }\n    return volume.toString();\n}\n// Format duration in minutes to human readable\nfunction formatDuration(minutes) {\n    if (minutes < 60) {\n        return `${minutes}m`;\n    } else if (minutes < 1440) {\n        const hours = Math.floor(minutes / 60);\n        const remainingMinutes = minutes % 60;\n        return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;\n    } else {\n        const days = Math.floor(minutes / 1440);\n        const remainingHours = Math.floor(minutes % 1440 / 60);\n        return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;\n    }\n}\n// Format date to local string\nfunction formatDate(date, options) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return dateObj.toLocaleDateString(undefined, {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n        ...options\n    });\n}\n// Calculate percentage change\nfunction calculatePercentageChange(oldValue, newValue) {\n    if (oldValue === 0) return 0;\n    return (newValue - oldValue) / oldValue * 100;\n}\n// Clamp value between min and max\nfunction clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max);\n}\n// Generate random ID\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// Debounce function\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) {\n            clearTimeout(timeout);\n        }\n        timeout = setTimeout(()=>{\n            func(...args);\n        }, wait);\n    };\n}\n// Throttle function\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n// Color utilities for trading\nconst colors = {\n    bullish: {\n        primary: \"#10B981\",\n        secondary: \"#D1FAE5\",\n        dark: \"#047857\"\n    },\n    bearish: {\n        primary: \"#EF4444\",\n        secondary: \"#FEE2E2\",\n        dark: \"#DC2626\"\n    },\n    neutral: {\n        primary: \"#6B7280\",\n        secondary: \"#F3F4F6\",\n        dark: \"#374151\"\n    },\n    warning: {\n        primary: \"#F59E0B\",\n        secondary: \"#FEF3C7\",\n        dark: \"#D97706\"\n    },\n    info: {\n        primary: \"#3B82F6\",\n        secondary: \"#DBEAFE\",\n        dark: \"#1D4ED8\"\n    }\n};\n// Get color based on wave direction\nfunction getWaveColor(direction) {\n    return direction === \"bullish\" ? colors.bullish.primary : colors.bearish.primary;\n}\n// Get color based on performance\nfunction getPerformanceColor(value, threshold = 0) {\n    if (value > threshold) return colors.bullish.primary;\n    if (value < threshold) return colors.bearish.primary;\n    return colors.neutral.primary;\n}\n// Validate email format\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n// Validate password strength\nfunction validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push(\"Password must be at least 8 characters long\");\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    }\n    if (!/[0-9]/.test(password)) {\n        errors.push(\"Password must contain at least one number\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./src/stores/auth-store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        login: async (credentials)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].login(credentials);\n                // Store token and user (only on client side)\n                if (false) {}\n                set({\n                    user: response.user,\n                    token: response.access_token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        register: async (data)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].register(data);\n                // After registration, automatically login\n                await get().login({\n                    username: data.username,\n                    password: data.password\n                });\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            // Remove from localStorage (only on client side)\n            if (false) {}\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        initialize: ()=>{\n            // Only run on client side\n            if (true) {\n                return;\n            }\n            const token = localStorage.getItem(\"auth_token\");\n            const userStr = localStorage.getItem(\"user\");\n            if (token && userStr) {\n                try {\n                    const user = JSON.parse(userStr);\n                    set({\n                        user,\n                        token,\n                        isAuthenticated: true,\n                        isLoading: false\n                    });\n                } catch  {\n                    // Clear corrupted data\n                    localStorage.removeItem(\"auth_token\");\n                    localStorage.removeItem(\"user\");\n                    set({\n                        user: null,\n                        token: null,\n                        isAuthenticated: false,\n                        isLoading: false\n                    });\n                }\n            } else {\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/auth-store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"adad23446b75\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ODE2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFkYWQyMzQ0NmI3NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(rsc)/./src/components/auth/auth-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"FOREX/TFEX Data Annotation Tool\",\n    description: \"Professional trading pattern analysis platform with Volume Profile Wave Analysis\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ3dDO0FBSXZELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0Msd0VBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2F1dGgvYXV0aC1wcm92aWRlcidcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0ZPUkVYL1RGRVggRGF0YSBBbm5vdGF0aW9uIFRvb2wnLFxuICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCB0cmFkaW5nIHBhdHRlcm4gYW5hbHlzaXMgcGxhdGZvcm0gd2l0aCBWb2x1bWUgUHJvZmlsZSBXYXZlIEFuYWx5c2lzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/login/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/auth/auth-provider.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/auth-provider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/auth-provider.tsx#AuthProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/zustand","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/tailwind-merge","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();