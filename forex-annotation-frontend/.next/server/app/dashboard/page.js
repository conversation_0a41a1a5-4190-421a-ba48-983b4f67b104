/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/auth-provider.tsx */ \"(ssr)/./src/components/auth/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZwbG90cHJvJTJGTGlicmFyeSUyRkNsb3VkU3RvcmFnZSUyRlN5bm9sb2d5RHJpdmUtUExvVERyaXZlJTJGTXlfTGlmZSUyRlN0b2NrJTIwTWFya2V0JTJGQ2xhdWRlJTIwQ29kZSUyRmZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGYXV0aCUyRmF1dGgtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBbU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kLz9mMGYzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3Bsb3Rwcm8vTGlicmFyeS9DbG91ZFN0b3JhZ2UvU3lub2xvZ3lEcml2ZS1QTG9URHJpdmUvTXlfTGlmZS9TdG9jayBNYXJrZXQvQ2xhdWRlIENvZGUvZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFzTCIsInNvdXJjZXMiOlsid2VicGFjazovL2ZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQvPzZjNDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcGxvdHByby9MaWJyYXJ5L0Nsb3VkU3RvcmFnZS9TeW5vbG9neURyaXZlLVBMb1REcml2ZS9NeV9MaWZlL1N0b2NrIE1hcmtldC9DbGF1ZGUgQ29kZS9mb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_protected_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/protected-route */ \"(ssr)/./src/components/auth/protected-route.tsx\");\n/* harmony import */ var _components_layout_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/navigation */ \"(ssr)/./src/components/layout/navigation.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_stats_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/stats-card */ \"(ssr)/./src/components/ui/stats-card.tsx\");\n/* harmony import */ var _components_ui_performance_chart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/performance-chart */ \"(ssr)/./src/components/ui/performance-chart.tsx\");\n/* harmony import */ var _components_ui_activity_feed__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/activity-feed */ \"(ssr)/./src/components/ui/activity-feed.tsx\");\n/* harmony import */ var _components_ui_quick_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/quick-actions */ \"(ssr)/./src/components/ui/quick-actions.tsx\");\n/* harmony import */ var _components_ui_market_status__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/market-status */ \"(ssr)/./src/components/ui/market-status.tsx\");\n/* harmony import */ var _stores_project_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/stores/project-store */ \"(ssr)/./src/stores/project-store.ts\");\n/* harmony import */ var _stores_volume_profile_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/stores/volume-profile-store */ \"(ssr)/./src/stores/volume-profile-store.ts\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,PieChart,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,PieChart,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,PieChart,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,PieChart,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,PieChart,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,PieChart,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { user } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_12__.useAuthStore)();\n    const { projects, currentProject, projectStats, fetchProjects, fetchProjectStats } = (0,_stores_project_store__WEBPACK_IMPORTED_MODULE_10__.useProjectStore)();\n    const { successStats, fetchSuccessStats } = (0,_stores_volume_profile_store__WEBPACK_IMPORTED_MODULE_11__.useVolumeProfileStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProjects();\n    }, [\n        fetchProjects\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentProject) {\n            fetchProjectStats(currentProject.id);\n            fetchSuccessStats(currentProject.id);\n        }\n    }, [\n        currentProject,\n        fetchProjectStats,\n        fetchSuccessStats\n    ]);\n    // Mock performance data for the chart\n    const performanceData = [\n        {\n            period: \"Jan\",\n            value: 65.5,\n            change: 5.2\n        },\n        {\n            period: \"Feb\",\n            value: 72.3,\n            change: 6.8\n        },\n        {\n            period: \"Mar\",\n            value: 68.9,\n            change: -3.4\n        },\n        {\n            period: \"Apr\",\n            value: 78.2,\n            change: 9.3\n        },\n        {\n            period: \"May\",\n            value: 82.6,\n            change: 4.4\n        },\n        {\n            period: \"Jun\",\n            value: 79.1,\n            change: -3.5\n        }\n    ];\n    const mockActivities = (0,_components_ui_activity_feed__WEBPACK_IMPORTED_MODULE_7__.generateMockActivities)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_protected_route__WEBPACK_IMPORTED_MODULE_2__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-background via-background to-muted/30\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_navigation__WEBPACK_IMPORTED_MODULE_3__.Navigation, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto py-8 px-4 space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-trading-primary/5 via-transparent to-trading-accent/5 rounded-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative p-8 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-gradient mb-3 fade-in\",\n                                                        children: [\n                                                            \"Welcome back, \",\n                                                            user?.full_name || user?.username,\n                                                            \"!\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground text-lg slide-up\",\n                                                        children: \"Your professional FOREX/TFEX trading pattern analysis dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden md:flex items-center space-x-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-xl bg-trading-primary/10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-8 h-8 text-trading-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                                    title: \"Total Projects\",\n                                    value: projects.length,\n                                    subtitle: \"Active trading projects\",\n                                    icon: _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                    trend: \"up\",\n                                    trendValue: \"+12%\",\n                                    className: \"scale-in\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                                    title: \"Volume Waves\",\n                                    value: projectStats?.volume_profile_waves.total || 0,\n                                    subtitle: `${projectStats?.volume_profile_waves.active || 0} active`,\n                                    icon: _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                    trend: \"up\",\n                                    trendValue: \"+8%\",\n                                    className: \"scale-in\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                                    title: \"Success Rate\",\n                                    value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.formatPercentage)(successStats?.success_rate || 0),\n                                    subtitle: \"Wave accuracy rate\",\n                                    icon: _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                    trend: successStats?.success_rate > 70 ? \"up\" : successStats?.success_rate > 50 ? \"neutral\" : \"down\",\n                                    trendValue: \"+2.3%\",\n                                    className: \"scale-in\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                                    title: \"Avg Duration\",\n                                    value: successStats ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.formatDuration)(successStats.average_duration_hours * 60) : \"0h\",\n                                    subtitle: \"Wave completion time\",\n                                    icon: _barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                    trend: \"down\",\n                                    trendValue: \"-15min\",\n                                    className: \"scale-in\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2 space-y-6\",\n                                    children: [\n                                        currentProject ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"trading-card p-6 fade-in\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-bold flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 rounded-xl bg-trading-primary/10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-6 h-6 text-trading-primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 141,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 140,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: currentProject.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 143,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-muted-foreground mt-2\",\n                                                                    children: currentProject.description || \"No description provided\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            className: \"btn-primary\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Analytics\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-4 rounded-xl bg-muted/50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-bull-600 mb-1\",\n                                                                    children: projectStats?.volume_profile_waves.completed || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Completed Waves\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-4 rounded-xl bg-muted/50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-trading-primary mb-1\",\n                                                                    children: projectStats?.volume_profile_waves.active || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Active Waves\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-4 rounded-xl bg-muted/50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-foreground mb-1\",\n                                                                    children: projectStats?.market_data_candles?.toLocaleString() || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Data Points\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"trading-card p-8 text-center fade-in\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-xl bg-trading-primary/10 w-fit mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-8 h-8 text-trading-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold mb-2\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground mb-6\",\n                                                    children: \"Create your first project to begin annotating FOREX/TFEX data\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    className: \"btn-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_PieChart_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Create New Project\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_performance_chart__WEBPACK_IMPORTED_MODULE_6__.PerformanceChart, {\n                                            title: \"Success Rate Trend\",\n                                            data: performanceData,\n                                            className: \"fade-in\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_quick_actions__WEBPACK_IMPORTED_MODULE_8__.QuickActions, {\n                                            className: \"fade-in\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_market_status__WEBPACK_IMPORTED_MODULE_9__.MarketStatus, {\n                                            className: \"fade-in\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_activity_feed__WEBPACK_IMPORTED_MODULE_7__.ActivityFeed, {\n                                            activities: mockActivities,\n                                            maxItems: 6,\n                                            className: \"fade-in\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/auth-provider.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/auth-provider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\n\nfunction AuthProvider({ children }) {\n    const initialize = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)((state)=>state.initialize);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize auth state from localStorage on app start\n        initialize();\n    }, [\n        initialize\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFd0M7QUFDVTtBQU0zQyxTQUFTRyxhQUFhLEVBQUVDLFFBQVEsRUFBcUI7SUFDMUQsTUFBTUMsYUFBYUgsZ0VBQVlBLENBQUNJLENBQUFBLFFBQVNBLE1BQU1ELFVBQVU7SUFFekRKLGdEQUFTQSxDQUFDO1FBQ1IsdURBQXVEO1FBQ3ZESTtJQUNGLEdBQUc7UUFBQ0E7S0FBVztJQUVmLHFCQUFPO2tCQUFHRDs7QUFDWiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4P2IzMzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJ0Avc3RvcmVzL2F1dGgtc3RvcmUnXG5cbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IEF1dGhQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IGluaXRpYWxpemUgPSB1c2VBdXRoU3RvcmUoc3RhdGUgPT4gc3RhdGUuaW5pdGlhbGl6ZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXRpYWxpemUgYXV0aCBzdGF0ZSBmcm9tIGxvY2FsU3RvcmFnZSBvbiBhcHAgc3RhcnRcbiAgICBpbml0aWFsaXplKClcbiAgfSwgW2luaXRpYWxpemVdKVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VBdXRoU3RvcmUiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsImluaXRpYWxpemUiLCJzdGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/protected-route.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/protected-route.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* harmony import */ var _components_client_only__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/client-only */ \"(ssr)/./src/components/client-only.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute auto */ \n\n\n\n\nfunction ProtectedRouteInner({ children, requiredRole }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, user, isLoading, initialize } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize auth store on mount\n        initialize();\n        setIsInitialized(true);\n    }, [\n        initialize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isInitialized) return;\n        // Redirect to login if not authenticated\n        if (!isLoading && !isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        // Check role-based access\n        if (isAuthenticated && user && requiredRole) {\n            const roleHierarchy = {\n                \"viewer\": 0,\n                \"analyst\": 1,\n                \"trader\": 2,\n                \"admin\": 3\n            };\n            const userLevel = roleHierarchy[user.role];\n            const requiredLevel = roleHierarchy[requiredRole];\n            if (userLevel < requiredLevel) {\n                router.push(\"/unauthorized\");\n                return;\n            }\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        user,\n        requiredRole,\n        router,\n        isInitialized\n    ]);\n    // Show loading while initializing or checking authentication\n    if (!isInitialized || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render children if not authenticated\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Redirecting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\nfunction ProtectedRoute(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_client_only__WEBPACK_IMPORTED_MODULE_4__.ClientOnly, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n                lineNumber: 84,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n            lineNumber: 83,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRouteInner, {\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/protected-route.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/protected-route.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/client-only.tsx":
/*!****************************************!*\
  !*** ./src/components/client-only.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientOnly: () => (/* binding */ ClientOnly)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientOnly auto */ \n\nfunction ClientOnly({ children, fallback }) {\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setHasMounted(true);\n    }, []);\n    if (!hasMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jbGllbnQtb25seS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTJDO0FBT3BDLFNBQVNFLFdBQVcsRUFBRUMsUUFBUSxFQUFFQyxRQUFRLEVBQW1CO0lBQ2hFLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHTCwrQ0FBUUEsQ0FBQztJQUU3Q0QsZ0RBQVNBLENBQUM7UUFDUk0sY0FBYztJQUNoQixHQUFHLEVBQUU7SUFFTCxJQUFJLENBQUNELFlBQVk7UUFDZixxQkFBTztzQkFBR0Q7O0lBQ1o7SUFFQSxxQkFBTztrQkFBR0Q7O0FBQ1oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvY2xpZW50LW9ubHkudHN4PzcxZmEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIENsaWVudE9ubHlQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgZmFsbGJhY2s/OiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENsaWVudE9ubHkoeyBjaGlsZHJlbiwgZmFsbGJhY2sgfTogQ2xpZW50T25seVByb3BzKSB7XG4gIGNvbnN0IFtoYXNNb3VudGVkLCBzZXRIYXNNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0SGFzTW91bnRlZCh0cnVlKVxuICB9LCBbXSlcblxuICBpZiAoIWhhc01vdW50ZWQpIHtcbiAgICByZXR1cm4gPD57ZmFsbGJhY2t9PC8+XG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+XG59Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiQ2xpZW50T25seSIsImNoaWxkcmVuIiwiZmFsbGJhY2siLCJoYXNNb3VudGVkIiwic2V0SGFzTW91bnRlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/client-only.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* harmony import */ var _stores_project_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/stores/project-store */ \"(ssr)/./src/stores/project-store.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,FolderOpen,LogOut,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,FolderOpen,LogOut,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,FolderOpen,LogOut,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,FolderOpen,LogOut,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,FolderOpen,LogOut,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,FolderOpen,LogOut,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\n\n\n\n\n\nfunction Navigation() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { user, logout } = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_5__.useAuthStore)();\n    const { currentProject, projects } = (0,_stores_project_store__WEBPACK_IMPORTED_MODULE_6__.useProjectStore)();\n    const handleLogout = ()=>{\n        logout();\n        router.push(\"/login\");\n    };\n    const isActive = (path)=>{\n        return pathname.startsWith(path);\n    };\n    const navItems = [\n        {\n            href: \"/dashboard\",\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            active: isActive(\"/dashboard\")\n        },\n        {\n            href: \"/projects\",\n            label: \"Projects\",\n            icon: _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            active: isActive(\"/projects\")\n        },\n        {\n            href: \"/annotation\",\n            label: \"Annotation\",\n            icon: _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            active: isActive(\"/annotation\")\n        },\n        {\n            href: \"/waves\",\n            label: \"Volume Waves\",\n            icon: _barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            active: isActive(\"/waves\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-gray-900 text-white p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold\",\n                                        children: \"FOREX Annotation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            currentProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-2 bg-gray-800 px-3 py-1 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-300\",\n                                        children: currentProject.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-1\",\n                        children: navItems.map((item)=>{\n                            const Icon = item.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: item.active ? \"secondary\" : \"ghost\",\n                                    size: \"sm\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex flex-col text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: user?.full_name || user?.username\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 capitalize\",\n                                        children: user?.role\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleLogout,\n                                className: \"text-gray-300 hover:text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden mt-4 flex space-x-1 overflow-x-auto\",\n                children: navItems.map((item)=>{\n                    const Icon = item.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: item.href,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: item.active ? \"secondary\" : \"ghost\",\n                            size: \"sm\",\n                            className: \"flex items-center space-x-1 whitespace-nowrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 15\n                        }, this)\n                    }, item.href, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            currentProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden mt-2 bg-gray-800 px-3 py-2 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_FolderOpen_LogOut_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-300\",\n                            children: currentProject.name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/layout/navigation.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/activity-feed.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/activity-feed.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivityFeed: () => (/* binding */ ActivityFeed),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateMockActivities: () => (/* binding */ generateMockActivities)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Target,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ActivityFeed,generateMockActivities,default auto */ \n\n\n\nconst getActivityIcon = (type, status)=>{\n    switch(type){\n        case \"wave_created\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, undefined);\n        case \"wave_completed\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 38,\n                columnNumber: 14\n            }, undefined);\n        case \"target_hit\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 40,\n                columnNumber: 14\n            }, undefined);\n        case \"target_missed\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 42,\n                columnNumber: 14\n            }, undefined);\n        case \"annotation_added\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 44,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 46,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst getStatusColor = (type, status)=>{\n    if (status) {\n        switch(status){\n            case \"success\":\n                return \"text-bull-600 bg-bull-100 dark:bg-bull-900/30\";\n            case \"error\":\n                return \"text-bear-600 bg-bear-100 dark:bg-bear-900/30\";\n            case \"warning\":\n                return \"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30\";\n            case \"info\":\n                return \"text-trading-primary bg-blue-100 dark:bg-blue-900/30\";\n        }\n    }\n    switch(type){\n        case \"target_hit\":\n        case \"wave_completed\":\n            return \"text-bull-600 bg-bull-100 dark:bg-bull-900/30\";\n        case \"target_missed\":\n            return \"text-bear-600 bg-bear-100 dark:bg-bear-900/30\";\n        default:\n            return \"text-trading-primary bg-blue-100 dark:bg-blue-900/30\";\n    }\n};\nconst formatTimeAgo = (date)=>{\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return \"Just now\";\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    return date.toLocaleDateString();\n};\nfunction ActivityFeed({ activities, className, showAll = false, maxItems = 10 }) {\n    const displayedActivities = showAll ? activities : activities.slice(0, maxItems);\n    if (!activities || activities.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"trading-card p-6\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4 flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Recent Activity\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center py-8 text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-12 h-12 mb-3 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center\",\n                            children: \"No recent activity\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-center mt-1\",\n                            children: \"Start trading to see your activity here\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"trading-card p-6\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Real-time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: displayedActivities.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-start space-x-3 p-3 rounded-lg border border-border/50\", \"hover:bg-muted/50 transition-colors duration-200\", \"slide-up\", index === 0 && \"animate-scale-in\"),\n                        style: {\n                            animationDelay: `${index * 50}ms`\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 p-2 rounded-full\", getStatusColor(activity.type, activity.status)),\n                                children: getActivityIcon(activity.type, activity.status)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-foreground truncate\",\n                                                children: activity.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground whitespace-nowrap ml-2\",\n                                                children: formatTimeAgo(activity.timestamp)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    activity.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1 line-clamp-2\",\n                                        children: activity.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    activity.value !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm font-medium\", activity.value > 0 ? \"text-bull-600\" : activity.value < 0 ? \"text-bear-600\" : \"text-muted-foreground\"),\n                                            children: [\n                                                activity.value > 0 && \"+\",\n                                                activity.value.toFixed(2),\n                                                activity.currency && ` ${activity.currency}`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, activity.id, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            !showAll && activities.length > maxItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"w-full text-center text-sm text-trading-primary hover:text-trading-accent transition-colors font-medium\",\n                    children: [\n                        \"View all \",\n                        activities.length,\n                        \" activities →\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/activity-feed.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n// Mock data generator for development/demo purposes\nconst generateMockActivities = ()=>{\n    const mockActivities = [\n        {\n            id: \"1\",\n            type: \"wave_created\",\n            title: \"New Volume Profile Wave Created\",\n            description: \"EUR/USD H1 - Wave starting at 1.0850\",\n            timestamp: new Date(Date.now() - 5 * 60 * 1000),\n            status: \"info\"\n        },\n        {\n            id: \"2\",\n            type: \"target_hit\",\n            title: \"Target Achieved\",\n            description: \"GBP/USD wave reached target price\",\n            timestamp: new Date(Date.now() - 15 * 60 * 1000),\n            status: \"success\",\n            value: 45.50,\n            currency: \"pips\"\n        },\n        {\n            id: \"3\",\n            type: \"wave_completed\",\n            title: \"Wave Analysis Completed\",\n            description: \"USD/JPY M30 analysis finished successfully\",\n            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n            status: \"success\"\n        },\n        {\n            id: \"4\",\n            type: \"target_missed\",\n            title: \"Target Missed\",\n            description: \"AUD/USD wave stopped out at resistance\",\n            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n            status: \"error\",\n            value: -23.75,\n            currency: \"pips\"\n        },\n        {\n            id: \"5\",\n            type: \"annotation_added\",\n            title: \"New Annotation Added\",\n            description: \"Support level marked on EUR/GBP chart\",\n            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n            status: \"info\"\n        }\n    ];\n    return mockActivities;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ActivityFeed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/activity-feed.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            success: \"bg-green-600 text-white hover:bg-green-700\",\n            warning: \"bg-yellow-600 text-white hover:bg-yellow-700\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/market-status.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/market-status.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketStatus: () => (/* binding */ MarketStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Globe,Minus,RefreshCw,TrendingDown,TrendingUp,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketStatus,default auto */ \n\n\n\nconst mockSessions = [\n    {\n        name: \"London\",\n        timezone: \"GMT\",\n        isOpen: true,\n        openTime: \"08:00\",\n        closeTime: \"17:00\",\n        status: \"open\"\n    },\n    {\n        name: \"New York\",\n        timezone: \"EST\",\n        isOpen: true,\n        openTime: \"08:00\",\n        closeTime: \"17:00\",\n        status: \"open\"\n    },\n    {\n        name: \"Tokyo\",\n        timezone: \"JST\",\n        isOpen: false,\n        openTime: \"09:00\",\n        closeTime: \"18:00\",\n        status: \"closed\"\n    },\n    {\n        name: \"Sydney\",\n        timezone: \"AEST\",\n        isOpen: false,\n        openTime: \"09:00\",\n        closeTime: \"17:00\",\n        status: \"closed\"\n    }\n];\nconst mockCurrencyPairs = [\n    {\n        symbol: \"EUR/USD\",\n        price: 1.0850,\n        change: 0.0025,\n        changePercent: 0.23,\n        bid: 1.0848,\n        ask: 1.0852,\n        spread: 0.4\n    },\n    {\n        symbol: \"GBP/USD\",\n        price: 1.2650,\n        change: -0.0045,\n        changePercent: -0.35,\n        bid: 1.2648,\n        ask: 1.2652,\n        spread: 0.4\n    },\n    {\n        symbol: \"USD/JPY\",\n        price: 148.75,\n        change: 0.85,\n        changePercent: 0.57,\n        bid: 148.73,\n        ask: 148.77,\n        spread: 0.4\n    },\n    {\n        symbol: \"AUD/USD\",\n        price: 0.6580,\n        change: -0.0015,\n        changePercent: -0.23,\n        bid: 0.6578,\n        ask: 0.6582,\n        spread: 0.4\n    }\n];\nfunction MarketStatus({ className, showPairs = true, autoRefresh = true, refreshInterval = 30000 }) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currencyPairs, setCurrencyPairs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockCurrencyPairs);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(()=>{\n            setLastUpdate(new Date());\n            // Simulate price updates\n            setCurrencyPairs((prev)=>prev.map((pair)=>({\n                        ...pair,\n                        price: pair.price + (Math.random() - 0.5) * 0.01,\n                        change: pair.change + (Math.random() - 0.5) * 0.005,\n                        changePercent: pair.changePercent + (Math.random() - 0.5) * 0.1\n                    })));\n        }, refreshInterval);\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh,\n        refreshInterval\n    ]);\n    const openSessions = mockSessions.filter((s)=>s.isOpen);\n    const marketStatus = openSessions.length > 0 ? \"open\" : \"closed\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"trading-card p-6 space-y-6\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Market Status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1 text-xs px-2 py-1 rounded-full\", isConnected ? \"text-bull-600 bg-bull-100 dark:bg-bull-900/30\" : \"text-bear-600 bg-bear-100 dark:bg-bear-900/30\"),\n                                children: [\n                                    isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isConnected ? \"Live\" : \"Offline\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 hover:bg-muted rounded transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-3\",\n                children: mockSessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-3 rounded-lg border transition-all duration-200\", session.isOpen ? \"bg-bull-50 border-bull-200 dark:bg-bull-900/20 dark:border-bull-800\" : \"bg-muted border-border\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-sm\",\n                                        children: session.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-2 h-2 rounded-full\", session.isOpen ? \"bg-bull-500 animate-pulse\" : \"bg-gray-400\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    session.openTime,\n                                    \" - \",\n                                    session.closeTime,\n                                    \" \",\n                                    session.timezone\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs font-medium mt-1 capitalize\", session.isOpen ? \"text-bull-600\" : \"text-muted-foreground\"),\n                                children: session.status.replace(\"-\", \" \")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, session.name, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-4 rounded-lg text-center\", marketStatus === \"open\" ? \"bg-bull-50 dark:bg-bull-900/20 border border-bull-200 dark:border-bull-800\" : \"bg-muted border border-border\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold\",\n                                children: [\n                                    \"Market is \",\n                                    marketStatus === \"open\" ? \"Open\" : \"Closed\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground\",\n                        children: openSessions.length > 0 ? `${openSessions.length} session${openSessions.length > 1 ? \"s\" : \"\"} active` : \"All sessions closed\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            showPairs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-sm\",\n                                children: \"Major Pairs\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Last: \",\n                                    lastUpdate.toLocaleTimeString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: currencyPairs.map((pair)=>{\n                            const isPositive = pair.change > 0;\n                            const TrendIcon = isPositive ? _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : pair.change < 0 ? _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : _barrel_optimize_names_Clock_Globe_Minus_RefreshCw_TrendingDown_TrendingUp_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-sm\",\n                                                children: pair.symbol\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1 text-xs\", isPositive ? \"text-bull-600\" : pair.change < 0 ? \"text-bear-600\" : \"text-muted-foreground\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendIcon, {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            isPositive && \"+\",\n                                                            pair.changePercent.toFixed(2),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-sm\",\n                                                children: pair.price.toFixed(pair.symbol.includes(\"JPY\") ? 2 : 4)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    pair.bid.toFixed(pair.symbol.includes(\"JPY\") ? 2 : 4),\n                                                    \"/\",\n                                                    pair.ask.toFixed(pair.symbol.includes(\"JPY\") ? 2 : 4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, pair.symbol, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/market-status.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MarketStatus);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/market-status.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/performance-chart.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/performance-chart.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceChart: () => (/* binding */ PerformanceChart),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ PerformanceChart,default auto */ \n\n\n\nfunction PerformanceChart({ title, data, className, height = 120, showTrend = true }) {\n    if (!data || data.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"trading-card p-6\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-32 text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No data available\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    const maxValue = Math.max(...data.map((d)=>d.value));\n    const minValue = Math.min(...data.map((d)=>d.value));\n    const range = maxValue - minValue || 1;\n    const overallTrend = data.length > 1 ? data[data.length - 1].value - data[0].value : 0;\n    const TrendIcon = overallTrend > 0 ? _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"] : overallTrend < 0 ? _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"] : _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    const trendColor = overallTrend > 0 ? \"text-bull-500\" : overallTrend < 0 ? \"text-bear-500\" : \"text-gray-500\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"trading-card p-6 group\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    showTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-1\", trendColor),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendIcon, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: [\n                                    overallTrend > 0 ? \"+\" : \"\",\n                                    overallTrend.toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    height\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 grid grid-rows-4 opacity-10\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-current\"\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"absolute inset-0 w-full h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                    id: \"chartGradient\",\n                                    x1: \"0%\",\n                                    y1: \"0%\",\n                                    x2: \"0%\",\n                                    y2: \"100%\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"0%\",\n                                            className: \"stop-color-trading-primary\",\n                                            stopOpacity: 0.3\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"100%\",\n                                            className: \"stop-color-trading-primary\",\n                                            stopOpacity: 0.05\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: `\n              M 0 ${height}\n              ${data.map((point, index)=>{\n                                    const x = index / (data.length - 1) * 100;\n                                    const y = height - (point.value - minValue) / range * height;\n                                    return `L ${x}% ${y}`;\n                                }).join(\" \")}\n              L 100% ${height}\n              Z\n            `,\n                                fill: \"url(#chartGradient)\",\n                                className: \"opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                points: data.map((point, index)=>{\n                                    const x = index / (data.length - 1) * 100;\n                                    const y = height - (point.value - minValue) / range * height;\n                                    return `${x}%,${y}`;\n                                }).join(\" \"),\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                className: \"text-trading-primary drop-shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            data.map((point, index)=>{\n                                const x = index / (data.length - 1) * 100;\n                                const y = height - (point.value - minValue) / range * height;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: `${x}%`,\n                                    cy: y,\n                                    r: \"3\",\n                                    fill: \"currentColor\",\n                                    className: \"text-trading-primary opacity-80 hover:opacity-100 transition-opacity group-hover:r-4\"\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-end justify-between text-xs text-muted-foreground px-1 pb-1\",\n                        children: data.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-1\",\n                                style: {\n                                    opacity: index === 0 || index === data.length - 1 ? 1 : 0.7\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border border-border rounded px-2 py-1 shadow-sm\",\n                                        children: [\n                                            point.value.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: point.period\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-4 pt-4 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Best\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-bull-600\",\n                                children: [\n                                    maxValue.toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Avg\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium\",\n                                children: [\n                                    (data.reduce((sum, d)=>sum + d.value, 0) / data.length).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Worst\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-bear-600\",\n                                children: [\n                                    minValue.toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/performance-chart.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformanceChart);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/performance-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/quick-actions.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/quick-actions.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: () => (/* binding */ QuickActions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Download,PlusCircle,Settings,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ QuickActions,default auto */ \n\n\n\n\nconst defaultActions = [\n    {\n        id: \"new-wave\",\n        label: \"New Wave\",\n        description: \"Create volume profile wave\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        onClick: ()=>console.log(\"New Wave\"),\n        variant: \"default\"\n    },\n    {\n        id: \"analytics\",\n        label: \"Analytics\",\n        description: \"View detailed analytics\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        onClick: ()=>console.log(\"Analytics\"),\n        variant: \"outline\"\n    },\n    {\n        id: \"active-waves\",\n        label: \"Active Waves\",\n        description: \"Monitor live waves\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        onClick: ()=>console.log(\"Active Waves\"),\n        variant: \"outline\",\n        badge: 3\n    },\n    {\n        id: \"new-annotation\",\n        label: \"Annotate\",\n        description: \"Add chart annotation\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        onClick: ()=>console.log(\"New Annotation\"),\n        variant: \"outline\"\n    },\n    {\n        id: \"export-data\",\n        label: \"Export\",\n        description: \"Export trading data\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        onClick: ()=>console.log(\"Export Data\"),\n        variant: \"outline\"\n    },\n    {\n        id: \"import-data\",\n        label: \"Import\",\n        description: \"Import market data\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        onClick: ()=>console.log(\"Import Data\"),\n        variant: \"outline\"\n    },\n    {\n        id: \"targets\",\n        label: \"Targets\",\n        description: \"Manage price targets\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        onClick: ()=>console.log(\"Targets\"),\n        variant: \"outline\"\n    },\n    {\n        id: \"quick-trade\",\n        label: \"Quick Setup\",\n        description: \"Fast trading setup\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        onClick: ()=>console.log(\"Quick Trade\"),\n        variant: \"secondary\"\n    }\n];\nfunction QuickActions({ actions = defaultActions, className, columns = 4 }) {\n    const gridColumns = {\n        2: \"grid-cols-2\",\n        3: \"grid-cols-1 sm:grid-cols-2 md:grid-cols-3\",\n        4: \"grid-cols-2 md:grid-cols-4\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"trading-card p-6\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Quick Actions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"text-xs\",\n                        children: \"Customize\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"grid gap-4\", gridColumns[columns]),\n                children: actions.map((action, index)=>{\n                    const Icon = action.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: action.variant || \"outline\",\n                        disabled: action.disabled,\n                        onClick: action.onClick,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-auto p-4 flex flex-col items-center space-y-3 group relative\", \"hover:scale-105 transition-all duration-200\", \"fade-in\"),\n                        style: {\n                            animationDelay: `${index * 50}ms`\n                        },\n                        children: [\n                            action.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-2 -right-2 bg-trading-primary text-white text-xs font-medium px-2 py-1 rounded-full min-w-[20px] h-5 flex items-center justify-center\",\n                                children: action.badge\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-3 rounded-xl transition-all duration-200\", action.variant === \"default\" ? \"bg-trading-primary/10 group-hover:bg-trading-primary/20\" : \"bg-muted group-hover:bg-muted/80\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-6 h-6\", action.variant === \"default\" ? \"text-trading-primary\" : \"text-foreground\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-sm leading-tight\",\n                                        children: action.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this),\n                                    action.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground leading-tight\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-trading-primary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, action.id, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-6 pt-4 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Download_PlusCircle_Settings_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Keyboard shortcuts available\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"text-xs hover:text-trading-primary\",\n                        children: \"View All →\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/quick-actions.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuickActions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/quick-actions.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/stats-card.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/stats-card.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ StatsCard,default auto */ \n\n\nfunction StatsCard({ title, value, subtitle, icon: Icon, trend = \"neutral\", trendValue, className, animate = true }) {\n    const trendColors = {\n        up: \"text-bull-600 bg-bull-50 dark:bg-bull-500/20 dark:text-bull-400\",\n        down: \"text-bear-600 bg-bear-50 dark:bg-bear-500/20 dark:text-bear-400\",\n        neutral: \"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"stats-card group\", animate && \"fade-in\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 rounded-xl bg-gradient-to-br from-trading-primary/10 to-trading-accent/10 group-hover:from-trading-primary/20 group-hover:to-trading-accent/20 transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"w-6 h-6 text-trading-primary\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    trendValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"performance-indicator\", trendColors[trend]),\n                        children: [\n                            trend === \"up\" && \"↗\",\n                            trend === \"down\" && \"↘\",\n                            trend === \"neutral\" && \"→\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: trendValue\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-muted-foreground tracking-wide\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-3xl font-bold text-foreground\", animate && \"counter-animation\"),\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-trading-primary/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/ui/stats-card.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StatsCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/stats-card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = \"v1\";\nclass ApiClient {\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: `${API_BASE_URL}/api/${API_VERSION}`,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = localStorage.getItem(\"auth_token\");\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            return config;\n        });\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            if (error.response?.status === 401) {\n                // Clear token on 401\n                localStorage.removeItem(\"auth_token\");\n                localStorage.removeItem(\"user\");\n                window.location.href = \"/login\";\n            }\n            return Promise.reject(error);\n        });\n    }\n    // Authentication endpoints\n    async login(credentials) {\n        const formData = new FormData();\n        formData.append(\"username\", credentials.username);\n        formData.append(\"password\", credentials.password);\n        const response = await this.client.post(\"/auth/login\", formData, {\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        return response.data;\n    }\n    async register(data) {\n        const response = await this.client.post(\"/auth/register\", data);\n        return response.data;\n    }\n    async getCurrentUser() {\n        const response = await this.client.get(\"/auth/me\");\n        return response.data;\n    }\n    async getUsers() {\n        const response = await this.client.get(\"/auth/users\");\n        return response.data;\n    }\n    // Project endpoints\n    async getProjects() {\n        const response = await this.client.get(\"/projects\");\n        return response.data;\n    }\n    async createProject(projectData) {\n        const response = await this.client.post(\"/projects\", projectData);\n        return response.data;\n    }\n    async getProject(id) {\n        const response = await this.client.get(`/projects/${id}`);\n        return response.data;\n    }\n    async updateProject(id, data) {\n        const response = await this.client.put(`/projects/${id}`, data);\n        return response.data;\n    }\n    async deleteProject(id) {\n        await this.client.delete(`/projects/${id}`);\n    }\n    async getProjectStats(id) {\n        const response = await this.client.get(`/projects/${id}/stats`);\n        return response.data;\n    }\n    // Volume Profile Wave endpoints\n    async createVolumeWave(data) {\n        const response = await this.client.post(\"/volume-waves\", data);\n        return response.data;\n    }\n    async getVolumeWaves(projectId, includeCompleted = true) {\n        const response = await this.client.get(\"/volume-waves\", {\n            params: {\n                project_id: projectId,\n                include_completed: includeCompleted\n            }\n        });\n        return response.data;\n    }\n    async getVolumeWave(id) {\n        const response = await this.client.get(`/volume-waves/${id}`);\n        return response.data;\n    }\n    async updateWaveCompletion(id, data) {\n        const response = await this.client.put(`/volume-waves/${id}/completion`, data);\n        return response.data;\n    }\n    async getActiveWaves(projectId) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/active`);\n        return response.data;\n    }\n    async getCompletedWaves(projectId, limit = 100) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/completed`, {\n            params: {\n                limit\n            }\n        });\n        return response.data;\n    }\n    async getWaveSuccessRate(projectId, minimumAccuracy = 70) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/success-rate`, {\n            params: {\n                minimum_accuracy: minimumAccuracy\n            }\n        });\n        return response.data;\n    }\n    async exportTrainingData(projectId) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/training-data`);\n        return response.data;\n    }\n    async deleteVolumeWave(id) {\n        await this.client.delete(`/volume-waves/${id}`);\n    }\n    async validateWaveFormula(id) {\n        const response = await this.client.get(`/volume-waves/${id}/formula-validation`);\n        return response.data;\n    }\n    // Annotation endpoints\n    async createAnnotation(data) {\n        const response = await this.client.post(\"/annotations\", data);\n        return response.data;\n    }\n    async getAnnotations(projectId, annotationType, validationStatus) {\n        const params = {\n            project_id: projectId\n        };\n        if (annotationType) params.annotation_type = annotationType;\n        if (validationStatus) params.validation_status = validationStatus;\n        const response = await this.client.get(\"/annotations\", {\n            params\n        });\n        return response.data;\n    }\n    async getAnnotation(id) {\n        const response = await this.client.get(`/annotations/${id}`);\n        return response.data;\n    }\n    async updateAnnotation(id, data) {\n        const response = await this.client.put(`/annotations/${id}`, data);\n        return response.data;\n    }\n    async deleteAnnotation(id) {\n        await this.client.delete(`/annotations/${id}`);\n    }\n    async getAnnotationSummary(projectId) {\n        const response = await this.client.get(`/annotations/projects/${projectId}/summary`);\n        return response.data;\n    }\n    async addPatternMetrics(annotationId, metrics) {\n        const response = await this.client.post(`/annotations/${annotationId}/metrics`, metrics);\n        return response.data;\n    }\n    async getAnnotationMetrics(annotationId) {\n        const response = await this.client.get(`/annotations/${annotationId}/metrics`);\n        return response.data;\n    }\n    // Custom request method for additional flexibility\n    async request(config) {\n        const response = await this.client.request(config);\n        return response.data;\n    }\n}\n// Export singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePercentageChange: () => (/* binding */ calculatePercentageChange),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatVolume: () => (/* binding */ formatVolume),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getPerformanceColor: () => (/* binding */ getPerformanceColor),\n/* harmony export */   getWaveColor: () => (/* binding */ getWaveColor),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format price with appropriate decimal places\nfunction formatPrice(price, decimals = 4) {\n    return price.toFixed(decimals);\n}\n// Format percentage\nfunction formatPercentage(value, decimals = 2) {\n    return `${value.toFixed(decimals)}%`;\n}\n// Format volume with K/M/B suffixes\nfunction formatVolume(volume) {\n    if (volume >= 1e9) {\n        return `${(volume / 1e9).toFixed(1)}B`;\n    } else if (volume >= 1e6) {\n        return `${(volume / 1e6).toFixed(1)}M`;\n    } else if (volume >= 1e3) {\n        return `${(volume / 1e3).toFixed(1)}K`;\n    }\n    return volume.toString();\n}\n// Format duration in minutes to human readable\nfunction formatDuration(minutes) {\n    if (minutes < 60) {\n        return `${minutes}m`;\n    } else if (minutes < 1440) {\n        const hours = Math.floor(minutes / 60);\n        const remainingMinutes = minutes % 60;\n        return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;\n    } else {\n        const days = Math.floor(minutes / 1440);\n        const remainingHours = Math.floor(minutes % 1440 / 60);\n        return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;\n    }\n}\n// Format date to local string\nfunction formatDate(date, options) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return dateObj.toLocaleDateString(undefined, {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n        ...options\n    });\n}\n// Calculate percentage change\nfunction calculatePercentageChange(oldValue, newValue) {\n    if (oldValue === 0) return 0;\n    return (newValue - oldValue) / oldValue * 100;\n}\n// Clamp value between min and max\nfunction clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max);\n}\n// Generate random ID\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// Debounce function\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) {\n            clearTimeout(timeout);\n        }\n        timeout = setTimeout(()=>{\n            func(...args);\n        }, wait);\n    };\n}\n// Throttle function\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n// Color utilities for trading\nconst colors = {\n    bullish: {\n        primary: \"#10B981\",\n        secondary: \"#D1FAE5\",\n        dark: \"#047857\"\n    },\n    bearish: {\n        primary: \"#EF4444\",\n        secondary: \"#FEE2E2\",\n        dark: \"#DC2626\"\n    },\n    neutral: {\n        primary: \"#6B7280\",\n        secondary: \"#F3F4F6\",\n        dark: \"#374151\"\n    },\n    warning: {\n        primary: \"#F59E0B\",\n        secondary: \"#FEF3C7\",\n        dark: \"#D97706\"\n    },\n    info: {\n        primary: \"#3B82F6\",\n        secondary: \"#DBEAFE\",\n        dark: \"#1D4ED8\"\n    }\n};\n// Get color based on wave direction\nfunction getWaveColor(direction) {\n    return direction === \"bullish\" ? colors.bullish.primary : colors.bearish.primary;\n}\n// Get color based on performance\nfunction getPerformanceColor(value, threshold = 0) {\n    if (value > threshold) return colors.bullish.primary;\n    if (value < threshold) return colors.bearish.primary;\n    return colors.neutral.primary;\n}\n// Validate email format\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n// Validate password strength\nfunction validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push(\"Password must be at least 8 characters long\");\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    }\n    if (!/[0-9]/.test(password)) {\n        errors.push(\"Password must contain at least one number\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./src/stores/auth-store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        login: async (credentials)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].login(credentials);\n                // Store token and user (only on client side)\n                if (false) {}\n                set({\n                    user: response.user,\n                    token: response.access_token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        register: async (data)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].register(data);\n                // After registration, automatically login\n                await get().login({\n                    username: data.username,\n                    password: data.password\n                });\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            // Remove from localStorage (only on client side)\n            if (false) {}\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        initialize: ()=>{\n            // Only run on client side\n            if (true) {\n                return;\n            }\n            const token = localStorage.getItem(\"auth_token\");\n            const userStr = localStorage.getItem(\"user\");\n            if (token && userStr) {\n                try {\n                    const user = JSON.parse(userStr);\n                    set({\n                        user,\n                        token,\n                        isAuthenticated: true,\n                        isLoading: false\n                    });\n                } catch  {\n                    // Clear corrupted data\n                    localStorage.removeItem(\"auth_token\");\n                    localStorage.removeItem(\"user\");\n                    set({\n                        user: null,\n                        token: null,\n                        isAuthenticated: false,\n                        isLoading: false\n                    });\n                }\n            } else {\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/auth-store.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/project-store.ts":
/*!*************************************!*\
  !*** ./src/stores/project-store.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useProjectStore: () => (/* binding */ useProjectStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\nconst useProjectStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        // Initial state\n        projects: [],\n        currentProject: null,\n        projectStats: null,\n        isLoading: false,\n        error: null,\n        // Actions\n        fetchProjects: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const projects = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getProjects();\n                set({\n                    projects,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to fetch projects\",\n                    isLoading: false\n                });\n            }\n        },\n        createProject: async (data)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const project = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createProject(data);\n                const { projects } = get();\n                set({\n                    projects: [\n                        ...projects,\n                        project\n                    ],\n                    currentProject: project,\n                    isLoading: false\n                });\n                return project;\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to create project\",\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        selectProject: (project)=>{\n            set({\n                currentProject: project,\n                projectStats: null\n            });\n            // Automatically fetch stats when project is selected\n            get().fetchProjectStats(project.id);\n        },\n        updateProject: async (id, data)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const updatedProject = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateProject(id, data);\n                const { projects, currentProject } = get();\n                const updatedProjects = projects.map((p)=>p.id === id ? updatedProject : p);\n                set({\n                    projects: updatedProjects,\n                    currentProject: currentProject?.id === id ? updatedProject : currentProject,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to update project\",\n                    isLoading: false\n                });\n            }\n        },\n        deleteProject: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteProject(id);\n                const { projects, currentProject } = get();\n                const filteredProjects = projects.filter((p)=>p.id !== id);\n                const newCurrentProject = currentProject?.id === id ? null : currentProject;\n                set({\n                    projects: filteredProjects,\n                    currentProject: newCurrentProject,\n                    projectStats: newCurrentProject ? get().projectStats : null,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to delete project\",\n                    isLoading: false\n                });\n            }\n        },\n        fetchProjectStats: async (id)=>{\n            try {\n                const stats = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getProjectStats(id);\n                set({\n                    projectStats: stats\n                });\n            } catch (error) {\n                console.error(\"Failed to fetch project stats:\", error);\n            // Don't set error state for stats fetch failures\n            }\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/project-store.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/volume-profile-store.ts":
/*!********************************************!*\
  !*** ./src/stores/volume-profile-store.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVolumeProfileStore: () => (/* binding */ useVolumeProfileStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\nconst initialAnnotationState = {\n    currentStep: 1,\n    startPoint: undefined,\n    volumeRange: undefined,\n    pocPrice: undefined,\n    targetPrice: undefined,\n    volumeProfile: undefined,\n    isLoading: false,\n    error: undefined\n};\nconst useVolumeProfileStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        // Initial state\n        waves: [],\n        activeWaves: [],\n        completedWaves: [],\n        currentWave: null,\n        annotationState: initialAnnotationState,\n        successStats: null,\n        isLoading: false,\n        error: null,\n        // Actions\n        fetchWaves: async (projectId, includeCompleted = true)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const waves = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getVolumeWaves(projectId, includeCompleted);\n                set({\n                    waves,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to fetch waves\",\n                    isLoading: false\n                });\n            }\n        },\n        fetchActiveWaves: async (projectId)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const activeWaves = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getActiveWaves(projectId);\n                set({\n                    activeWaves,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to fetch active waves\",\n                    isLoading: false\n                });\n            }\n        },\n        fetchCompletedWaves: async (projectId, limit = 100)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const completedWaves = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getCompletedWaves(projectId, limit);\n                set({\n                    completedWaves,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to fetch completed waves\",\n                    isLoading: false\n                });\n            }\n        },\n        createWave: async (data)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const wave = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createVolumeWave(data);\n                const { waves, activeWaves } = get();\n                set({\n                    waves: [\n                        ...waves,\n                        wave\n                    ],\n                    activeWaves: [\n                        ...activeWaves,\n                        wave\n                    ],\n                    isLoading: false\n                });\n                return wave;\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to create wave\",\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        getWave: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const wave = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getVolumeWave(id);\n                set({\n                    currentWave: wave,\n                    isLoading: false\n                });\n                return wave;\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to fetch wave details\",\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        updateWaveCompletion: async (id, data)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const updatedWave = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateWaveCompletion(id, data);\n                const { waves, activeWaves, completedWaves } = get();\n                // Update in all relevant arrays\n                const updatedWaves = waves.map((w)=>w.id === id ? updatedWave : w);\n                const updatedActiveWaves = activeWaves.filter((w)=>w.id !== id);\n                const updatedCompletedWaves = [\n                    ...completedWaves,\n                    updatedWave\n                ];\n                set({\n                    waves: updatedWaves,\n                    activeWaves: updatedActiveWaves,\n                    completedWaves: updatedCompletedWaves,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to update wave completion\",\n                    isLoading: false\n                });\n            }\n        },\n        deleteWave: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteVolumeWave(id);\n                const { waves, activeWaves, completedWaves } = get();\n                set({\n                    waves: waves.filter((w)=>w.id !== id),\n                    activeWaves: activeWaves.filter((w)=>w.id !== id),\n                    completedWaves: completedWaves.filter((w)=>w.id !== id),\n                    currentWave: get().currentWave?.id === id ? null : get().currentWave,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to delete wave\",\n                    isLoading: false\n                });\n            }\n        },\n        fetchSuccessStats: async (projectId, minimumAccuracy = 70)=>{\n            try {\n                const stats = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getWaveSuccessRate(projectId, minimumAccuracy);\n                set({\n                    successStats: stats\n                });\n            } catch (error) {\n                console.error(\"Failed to fetch success stats:\", error);\n            }\n        },\n        exportTrainingData: async (projectId)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].exportTrainingData(projectId);\n                set({\n                    isLoading: false\n                });\n                return data;\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to export training data\",\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        validateWaveFormula: async (id)=>{\n            try {\n                const validation = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].validateWaveFormula(id);\n                return validation;\n            } catch (error) {\n                set({\n                    error: error.response?.data?.detail || \"Failed to validate wave formula\"\n                });\n                throw error;\n            }\n        },\n        // Annotation workflow actions\n        setAnnotationStep: (step)=>{\n            set({\n                annotationState: {\n                    ...get().annotationState,\n                    currentStep: step\n                }\n            });\n        },\n        setStartPoint: (startPoint)=>{\n            set({\n                annotationState: {\n                    ...get().annotationState,\n                    startPoint\n                }\n            });\n        },\n        setVolumeRange: (volumeRange)=>{\n            set({\n                annotationState: {\n                    ...get().annotationState,\n                    volumeRange\n                }\n            });\n        },\n        setPocPrice: (pocPrice)=>{\n            set({\n                annotationState: {\n                    ...get().annotationState,\n                    pocPrice\n                }\n            });\n        },\n        setTargetPrice: (targetPrice)=>{\n            set({\n                annotationState: {\n                    ...get().annotationState,\n                    targetPrice\n                }\n            });\n        },\n        resetAnnotationState: ()=>{\n            set({\n                annotationState: initialAnnotationState\n            });\n        },\n        // Utility\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        setCurrentWave: (wave)=>{\n            set({\n                currentWave: wave\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/volume-profile-store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"adad23446b75\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ODE2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFkYWQyMzQ0NmI3NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/dashboard/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(rsc)/./src/components/auth/auth-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"FOREX/TFEX Data Annotation Tool\",\n    description: \"Professional trading pattern analysis platform with Volume Profile Wave Analysis\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ3dDO0FBSXZELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0Msd0VBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2F1dGgvYXV0aC1wcm92aWRlcidcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0ZPUkVYL1RGRVggRGF0YSBBbm5vdGF0aW9uIFRvb2wnLFxuICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCB0cmFkaW5nIHBhdHRlcm4gYW5hbHlzaXMgcGxhdGZvcm0gd2l0aCBWb2x1bWUgUHJvZmlsZSBXYXZlIEFuYWx5c2lzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth/auth-provider.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/auth-provider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/auth-provider.tsx#AuthProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/zustand","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/tailwind-merge","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();