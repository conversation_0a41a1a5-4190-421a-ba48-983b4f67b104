/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/auth-provider.tsx */ \"(ssr)/./src/components/auth/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcGxvdHBybyUyRkxpYnJhcnklMkZDbG91ZFN0b3JhZ2UlMkZTeW5vbG9neURyaXZlLVBMb1REcml2ZSUyRk15X0xpZmUlMkZTdG9jayUyME1hcmtldCUyRkNsYXVkZSUyMENvZGUlMkZmb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZwbG90cHJvJTJGTGlicmFyeSUyRkNsb3VkU3RvcmFnZSUyRlN5bm9sb2d5RHJpdmUtUExvVERyaXZlJTJGTXlfTGlmZSUyRlN0b2NrJTIwTWFya2V0JTJGQ2xhdWRlJTIwQ29kZSUyRmZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGYXV0aCUyRmF1dGgtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBbU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JleC1hbm5vdGF0aW9uLWZyb250ZW5kLz9mMGYzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3Bsb3Rwcm8vTGlicmFyeS9DbG91ZFN0b3JhZ2UvU3lub2xvZ3lEcml2ZS1QTG9URHJpdmUvTXlfTGlmZS9TdG9jayBNYXJrZXQvQ2xhdWRlIENvZGUvZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fcomponents%2Fauth%2Fauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/auth-provider.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/auth-provider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/auth-store */ \"(ssr)/./src/stores/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\n\nfunction AuthProvider({ children }) {\n    const initialize = (0,_stores_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)((state)=>state.initialize);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize auth state from localStorage on app start\n        initialize();\n    }, [\n        initialize\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFd0M7QUFDVTtBQU0zQyxTQUFTRyxhQUFhLEVBQUVDLFFBQVEsRUFBcUI7SUFDMUQsTUFBTUMsYUFBYUgsZ0VBQVlBLENBQUNJLENBQUFBLFFBQVNBLE1BQU1ELFVBQVU7SUFFekRKLGdEQUFTQSxDQUFDO1FBQ1IsdURBQXVEO1FBQ3ZESTtJQUNGLEdBQUc7UUFBQ0E7S0FBVztJQUVmLHFCQUFPO2tCQUFHRDs7QUFDWiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9hdXRoL2F1dGgtcHJvdmlkZXIudHN4P2IzMzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJ0Avc3RvcmVzL2F1dGgtc3RvcmUnXG5cbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IEF1dGhQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IGluaXRpYWxpemUgPSB1c2VBdXRoU3RvcmUoc3RhdGUgPT4gc3RhdGUuaW5pdGlhbGl6ZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXRpYWxpemUgYXV0aCBzdGF0ZSBmcm9tIGxvY2FsU3RvcmFnZSBvbiBhcHAgc3RhcnRcbiAgICBpbml0aWFsaXplKClcbiAgfSwgW2luaXRpYWxpemVdKVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VBdXRoU3RvcmUiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsImluaXRpYWxpemUiLCJzdGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_VERSION = \"v1\";\nclass ApiClient {\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: `${API_BASE_URL}/api/${API_VERSION}`,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = localStorage.getItem(\"auth_token\");\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            return config;\n        });\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            if (error.response?.status === 401) {\n                // Clear token on 401\n                localStorage.removeItem(\"auth_token\");\n                localStorage.removeItem(\"user\");\n                window.location.href = \"/login\";\n            }\n            return Promise.reject(error);\n        });\n    }\n    // Authentication endpoints\n    async login(credentials) {\n        const formData = new FormData();\n        formData.append(\"username\", credentials.username);\n        formData.append(\"password\", credentials.password);\n        const response = await this.client.post(\"/auth/login\", formData, {\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        return response.data;\n    }\n    async register(data) {\n        const response = await this.client.post(\"/auth/register\", data);\n        return response.data;\n    }\n    async getCurrentUser() {\n        const response = await this.client.get(\"/auth/me\");\n        return response.data;\n    }\n    async getUsers() {\n        const response = await this.client.get(\"/auth/users\");\n        return response.data;\n    }\n    // Project endpoints\n    async getProjects() {\n        const response = await this.client.get(\"/projects\");\n        return response.data;\n    }\n    async createProject(projectData) {\n        const response = await this.client.post(\"/projects\", projectData);\n        return response.data;\n    }\n    async getProject(id) {\n        const response = await this.client.get(`/projects/${id}`);\n        return response.data;\n    }\n    async updateProject(id, data) {\n        const response = await this.client.put(`/projects/${id}`, data);\n        return response.data;\n    }\n    async deleteProject(id) {\n        await this.client.delete(`/projects/${id}`);\n    }\n    async getProjectStats(id) {\n        const response = await this.client.get(`/projects/${id}/stats`);\n        return response.data;\n    }\n    // Volume Profile Wave endpoints\n    async createVolumeWave(data) {\n        const response = await this.client.post(\"/volume-waves\", data);\n        return response.data;\n    }\n    async getVolumeWaves(projectId, includeCompleted = true) {\n        const response = await this.client.get(\"/volume-waves\", {\n            params: {\n                project_id: projectId,\n                include_completed: includeCompleted\n            }\n        });\n        return response.data;\n    }\n    async getVolumeWave(id) {\n        const response = await this.client.get(`/volume-waves/${id}`);\n        return response.data;\n    }\n    async updateWaveCompletion(id, data) {\n        const response = await this.client.put(`/volume-waves/${id}/completion`, data);\n        return response.data;\n    }\n    async getActiveWaves(projectId) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/active`);\n        return response.data;\n    }\n    async getCompletedWaves(projectId, limit = 100) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/completed`, {\n            params: {\n                limit\n            }\n        });\n        return response.data;\n    }\n    async getWaveSuccessRate(projectId, minimumAccuracy = 70) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/success-rate`, {\n            params: {\n                minimum_accuracy: minimumAccuracy\n            }\n        });\n        return response.data;\n    }\n    async exportTrainingData(projectId) {\n        const response = await this.client.get(`/volume-waves/projects/${projectId}/training-data`);\n        return response.data;\n    }\n    async deleteVolumeWave(id) {\n        await this.client.delete(`/volume-waves/${id}`);\n    }\n    async validateWaveFormula(id) {\n        const response = await this.client.get(`/volume-waves/${id}/formula-validation`);\n        return response.data;\n    }\n    // Annotation endpoints\n    async createAnnotation(data) {\n        const response = await this.client.post(\"/annotations\", data);\n        return response.data;\n    }\n    async getAnnotations(projectId, annotationType, validationStatus) {\n        const params = {\n            project_id: projectId\n        };\n        if (annotationType) params.annotation_type = annotationType;\n        if (validationStatus) params.validation_status = validationStatus;\n        const response = await this.client.get(\"/annotations\", {\n            params\n        });\n        return response.data;\n    }\n    async getAnnotation(id) {\n        const response = await this.client.get(`/annotations/${id}`);\n        return response.data;\n    }\n    async updateAnnotation(id, data) {\n        const response = await this.client.put(`/annotations/${id}`, data);\n        return response.data;\n    }\n    async deleteAnnotation(id) {\n        await this.client.delete(`/annotations/${id}`);\n    }\n    async getAnnotationSummary(projectId) {\n        const response = await this.client.get(`/annotations/projects/${projectId}/summary`);\n        return response.data;\n    }\n    async addPatternMetrics(annotationId, metrics) {\n        const response = await this.client.post(`/annotations/${annotationId}/metrics`, metrics);\n        return response.data;\n    }\n    async getAnnotationMetrics(annotationId) {\n        const response = await this.client.get(`/annotations/${annotationId}/metrics`);\n        return response.data;\n    }\n    // Custom request method for additional flexibility\n    async request(config) {\n        const response = await this.client.request(config);\n        return response.data;\n    }\n}\n// Export singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUU7QUFpQmpFLE1BQU1DLGVBQWVDLHVCQUFvQyxJQUFJO0FBQzdELE1BQU1HLGNBQWM7QUFFcEIsTUFBTUM7SUFHSkMsYUFBYztRQUNaLElBQUksQ0FBQ0MsTUFBTSxHQUFHUiw2Q0FBS0EsQ0FBQ1MsTUFBTSxDQUFDO1lBQ3pCQyxTQUFTLENBQUMsRUFBRVQsYUFBYSxLQUFLLEVBQUVJLFlBQVksQ0FBQztZQUM3Q00sU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUVBLHdDQUF3QztRQUN4QyxJQUFJLENBQUNILE1BQU0sQ0FBQ0ksWUFBWSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxDQUFDQztZQUNwQyxNQUFNQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMsSUFBSUYsT0FBTztnQkFDVEQsT0FBT0osT0FBTyxDQUFDUSxhQUFhLEdBQUcsQ0FBQyxPQUFPLEVBQUVILE1BQU0sQ0FBQztZQUNsRDtZQUNBLE9BQU9EO1FBQ1Q7UUFFQSwwQ0FBMEM7UUFDMUMsSUFBSSxDQUFDUCxNQUFNLENBQUNJLFlBQVksQ0FBQ1EsUUFBUSxDQUFDTixHQUFHLENBQ25DLENBQUNNLFdBQWFBLFVBQ2QsQ0FBQ0M7WUFDQyxJQUFJQSxNQUFNRCxRQUFRLEVBQUVFLFdBQVcsS0FBSztnQkFDbEMscUJBQXFCO2dCQUNyQkwsYUFBYU0sVUFBVSxDQUFDO2dCQUN4Qk4sYUFBYU0sVUFBVSxDQUFDO2dCQUN4QkMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7WUFDekI7WUFDQSxPQUFPQyxRQUFRQyxNQUFNLENBQUNQO1FBQ3hCO0lBRUo7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTVEsTUFBTUMsV0FBNkIsRUFBeUI7UUFDaEUsTUFBTUMsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFlBQVlILFlBQVlJLFFBQVE7UUFDaERILFNBQVNFLE1BQU0sQ0FBQyxZQUFZSCxZQUFZSyxRQUFRO1FBRWhELE1BQU1mLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQzRCLElBQUksQ0FBQyxlQUFlTCxVQUFVO1lBQy9EcEIsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUNBLE9BQU9TLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsTUFBTUMsU0FBU0QsSUFBa0IsRUFBaUI7UUFDaEQsTUFBTWpCLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQzRCLElBQUksQ0FBQyxrQkFBa0JDO1FBQzFELE9BQU9qQixTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU1FLGlCQUFnQztRQUNwQyxNQUFNbkIsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0MsR0FBRyxDQUFDO1FBQ3ZDLE9BQU9wQixTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU1JLFdBQTRCO1FBQ2hDLE1BQU1yQixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNnQyxHQUFHLENBQUM7UUFDdkMsT0FBT3BCLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsb0JBQW9CO0lBQ3BCLE1BQU1LLGNBQWtDO1FBQ3RDLE1BQU10QixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNnQyxHQUFHLENBQUM7UUFDdkMsT0FBT3BCLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsTUFBTU0sY0FBY0MsV0FBaUUsRUFBb0I7UUFDdkcsTUFBTXhCLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQzRCLElBQUksQ0FBQyxhQUFhUTtRQUNyRCxPQUFPeEIsU0FBU2lCLElBQUk7SUFDdEI7SUFFQSxNQUFNUSxXQUFXQyxFQUFVLEVBQW9CO1FBQzdDLE1BQU0xQixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNnQyxHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUVNLEdBQUcsQ0FBQztRQUN4RCxPQUFPMUIsU0FBU2lCLElBQUk7SUFDdEI7SUFFQSxNQUFNVSxjQUFjRCxFQUFVLEVBQUVULElBQXNCLEVBQW9CO1FBQ3hFLE1BQU1qQixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUN3QyxHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUVGLEdBQUcsQ0FBQyxFQUFFVDtRQUMxRCxPQUFPakIsU0FBU2lCLElBQUk7SUFDdEI7SUFFQSxNQUFNWSxjQUFjSCxFQUFVLEVBQWlCO1FBQzdDLE1BQU0sSUFBSSxDQUFDdEMsTUFBTSxDQUFDMEMsTUFBTSxDQUFDLENBQUMsVUFBVSxFQUFFSixHQUFHLENBQUM7SUFDNUM7SUFFQSxNQUFNSyxnQkFBZ0JMLEVBQVUsRUFBeUI7UUFDdkQsTUFBTTFCLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQ2dDLEdBQUcsQ0FBQyxDQUFDLFVBQVUsRUFBRU0sR0FBRyxNQUFNLENBQUM7UUFDOUQsT0FBTzFCLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsZ0NBQWdDO0lBQ2hDLE1BQU1lLGlCQUFpQmYsSUFBNkIsRUFBOEI7UUFDaEYsTUFBTWpCLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQzRCLElBQUksQ0FBQyxpQkFBaUJDO1FBQ3pELE9BQU9qQixTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU1nQixlQUNKQyxTQUFpQixFQUNqQkMsbUJBQTRCLElBQUksRUFDRjtRQUM5QixNQUFNbkMsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0MsR0FBRyxDQUFDLGlCQUFpQjtZQUN0RGdCLFFBQVE7Z0JBQUVDLFlBQVlIO2dCQUFXSSxtQkFBbUJIO1lBQWlCO1FBQ3ZFO1FBQ0EsT0FBT25DLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsTUFBTXNCLGNBQWNiLEVBQVUsRUFBaUM7UUFDN0QsTUFBTTFCLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQ2dDLEdBQUcsQ0FBQyxDQUFDLGNBQWMsRUFBRU0sR0FBRyxDQUFDO1FBQzVELE9BQU8xQixTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU11QixxQkFDSmQsRUFBVSxFQUNWVCxJQUFpQyxFQUNMO1FBQzVCLE1BQU1qQixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUN3QyxHQUFHLENBQUMsQ0FBQyxjQUFjLEVBQUVGLEdBQUcsV0FBVyxDQUFDLEVBQUVUO1FBQ3pFLE9BQU9qQixTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU13QixlQUFlUCxTQUFpQixFQUFnQztRQUNwRSxNQUFNbEMsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0MsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUVjLFVBQVUsT0FBTyxDQUFDO1FBQ25GLE9BQU9sQyxTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU15QixrQkFBa0JSLFNBQWlCLEVBQUVTLFFBQWdCLEdBQUcsRUFBZ0M7UUFDNUYsTUFBTTNDLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQ2dDLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFYyxVQUFVLFVBQVUsQ0FBQyxFQUFFO1lBQ3RGRSxRQUFRO2dCQUFFTztZQUFNO1FBQ2xCO1FBQ0EsT0FBTzNDLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsTUFBTTJCLG1CQUNKVixTQUFpQixFQUNqQlcsa0JBQTBCLEVBQUUsRUFDRDtRQUMzQixNQUFNN0MsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0MsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUVjLFVBQVUsYUFBYSxDQUFDLEVBQUU7WUFDekZFLFFBQVE7Z0JBQUVVLGtCQUFrQkQ7WUFBZ0I7UUFDOUM7UUFDQSxPQUFPN0MsU0FBU2lCLElBQUk7SUFDdEI7SUFFQSxNQUFNOEIsbUJBQW1CYixTQUFpQixFQUFnQjtRQUN4RCxNQUFNbEMsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0MsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUVjLFVBQVUsY0FBYyxDQUFDO1FBQzFGLE9BQU9sQyxTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU0rQixpQkFBaUJ0QixFQUFVLEVBQWlCO1FBQ2hELE1BQU0sSUFBSSxDQUFDdEMsTUFBTSxDQUFDMEMsTUFBTSxDQUFDLENBQUMsY0FBYyxFQUFFSixHQUFHLENBQUM7SUFDaEQ7SUFFQSxNQUFNdUIsb0JBQW9CdkIsRUFBVSxFQVNqQztRQUNELE1BQU0xQixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNnQyxHQUFHLENBQUMsQ0FBQyxjQUFjLEVBQUVNLEdBQUcsbUJBQW1CLENBQUM7UUFDL0UsT0FBTzFCLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1pQyxpQkFBaUJqQyxJQUE2QixFQUF1QjtRQUN6RSxNQUFNakIsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDNEIsSUFBSSxDQUFDLGdCQUFnQkM7UUFDeEQsT0FBT2pCLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsTUFBTWtDLGVBQ0pqQixTQUFpQixFQUNqQmtCLGNBQXVCLEVBQ3ZCQyxnQkFBeUIsRUFDRjtRQUN2QixNQUFNakIsU0FBYztZQUFFQyxZQUFZSDtRQUFVO1FBQzVDLElBQUlrQixnQkFBZ0JoQixPQUFPa0IsZUFBZSxHQUFHRjtRQUM3QyxJQUFJQyxrQkFBa0JqQixPQUFPbUIsaUJBQWlCLEdBQUdGO1FBRWpELE1BQU1yRCxXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNnQyxHQUFHLENBQUMsZ0JBQWdCO1lBQUVnQjtRQUFPO1FBQ2hFLE9BQU9wQyxTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU11QyxjQUFjOUIsRUFBVSxFQUF1QjtRQUNuRCxNQUFNMUIsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0MsR0FBRyxDQUFDLENBQUMsYUFBYSxFQUFFTSxHQUFHLENBQUM7UUFDM0QsT0FBTzFCLFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsTUFBTXdDLGlCQUFpQi9CLEVBQVUsRUFBRVQsSUFBNkIsRUFBdUI7UUFDckYsTUFBTWpCLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQ3dDLEdBQUcsQ0FBQyxDQUFDLGFBQWEsRUFBRUYsR0FBRyxDQUFDLEVBQUVUO1FBQzdELE9BQU9qQixTQUFTaUIsSUFBSTtJQUN0QjtJQUVBLE1BQU15QyxpQkFBaUJoQyxFQUFVLEVBQWlCO1FBQ2hELE1BQU0sSUFBSSxDQUFDdEMsTUFBTSxDQUFDMEMsTUFBTSxDQUFDLENBQUMsYUFBYSxFQUFFSixHQUFHLENBQUM7SUFDL0M7SUFFQSxNQUFNaUMscUJBQXFCekIsU0FBaUIsRUFBOEI7UUFDeEUsTUFBTWxDLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQ2dDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFYyxVQUFVLFFBQVEsQ0FBQztRQUNuRixPQUFPbEMsU0FBU2lCLElBQUk7SUFDdEI7SUFFQSxNQUFNMkMsa0JBQWtCQyxZQUFvQixFQUFFQyxPQUFjLEVBQWtCO1FBQzVFLE1BQU05RCxXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUM0QixJQUFJLENBQUMsQ0FBQyxhQUFhLEVBQUU2QyxhQUFhLFFBQVEsQ0FBQyxFQUFFQztRQUNoRixPQUFPOUQsU0FBU2lCLElBQUk7SUFDdEI7SUFFQSxNQUFNOEMscUJBQXFCRixZQUFvQixFQUFrQjtRQUMvRCxNQUFNN0QsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0MsR0FBRyxDQUFDLENBQUMsYUFBYSxFQUFFeUMsYUFBYSxRQUFRLENBQUM7UUFDN0UsT0FBTzdELFNBQVNpQixJQUFJO0lBQ3RCO0lBRUEsbURBQW1EO0lBQ25ELE1BQU14QixRQUFXRSxNQUEwQixFQUFjO1FBQ3ZELE1BQU1LLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQ0ssT0FBTyxDQUFDRTtRQUMzQyxPQUFPSyxTQUFTaUIsSUFBSTtJQUN0QjtBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLE1BQU0rQyxZQUFZLElBQUk5RSxZQUFZO0FBQ3pDLGlFQUFlOEUsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZvcmV4LWFubm90YXRpb24tZnJvbnRlbmQvLi9zcmMvbGliL2FwaS50cz8yZmFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcywgeyBBeGlvc0luc3RhbmNlLCBBeGlvc1JlcXVlc3RDb25maWcgfSBmcm9tICdheGlvcyc7XG5pbXBvcnQgeyBBdXRoUmVzcG9uc2UsIExvZ2luQ3JlZGVudGlhbHMsIFJlZ2lzdGVyRGF0YSwgVXNlciB9IGZyb20gJ0AvdHlwZXMvYXV0aCc7XG5pbXBvcnQgeyBQcm9qZWN0LCBQcm9qZWN0U3RhdHMgfSBmcm9tICdAL3R5cGVzL21hcmtldC1kYXRhJztcbmltcG9ydCB7IFxuICBWb2x1bWVQcm9maWxlV2F2ZSwgXG4gIENyZWF0ZVZvbHVtZVdhdmVSZXF1ZXN0LCBcbiAgVXBkYXRlV2F2ZUNvbXBsZXRpb25SZXF1ZXN0LFxuICBXYXZlV2l0aERpc3RyaWJ1dGlvbixcbiAgV2F2ZVN1Y2Nlc3NTdGF0cyBcbn0gZnJvbSAnQC90eXBlcy92b2x1bWUtcHJvZmlsZSc7XG5pbXBvcnQgeyBcbiAgQW5ub3RhdGlvbiwgXG4gIENyZWF0ZUFubm90YXRpb25SZXF1ZXN0LCBcbiAgVXBkYXRlQW5ub3RhdGlvblJlcXVlc3QsIFxuICBBbm5vdGF0aW9uU3VtbWFyeSBcbn0gZnJvbSAnQC90eXBlcy9hbm5vdGF0aW9ucyc7XG5cbmNvbnN0IEFQSV9CQVNFX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJztcbmNvbnN0IEFQSV9WRVJTSU9OID0gJ3YxJztcblxuY2xhc3MgQXBpQ2xpZW50IHtcbiAgcHJpdmF0ZSBjbGllbnQ6IEF4aW9zSW5zdGFuY2U7XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5jbGllbnQgPSBheGlvcy5jcmVhdGUoe1xuICAgICAgYmFzZVVSTDogYCR7QVBJX0JBU0VfVVJMfS9hcGkvJHtBUElfVkVSU0lPTn1gLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIFJlcXVlc3QgaW50ZXJjZXB0b3IgdG8gYWRkIGF1dGggdG9rZW5cbiAgICB0aGlzLmNsaWVudC5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoKGNvbmZpZykgPT4ge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYXV0aF90b2tlbicpO1xuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcbiAgICAgIH1cbiAgICAgIHJldHVybiBjb25maWc7XG4gICAgfSk7XG5cbiAgICAvLyBSZXNwb25zZSBpbnRlcmNlcHRvciBmb3IgZXJyb3IgaGFuZGxpbmdcbiAgICB0aGlzLmNsaWVudC5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKFxuICAgICAgKHJlc3BvbnNlKSA9PiByZXNwb25zZSxcbiAgICAgIChlcnJvcikgPT4ge1xuICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxKSB7XG4gICAgICAgICAgLy8gQ2xlYXIgdG9rZW4gb24gNDAxXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhfdG9rZW4nKTtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcicpO1xuICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbic7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcbiAgICAgIH1cbiAgICApO1xuICB9XG5cbiAgLy8gQXV0aGVudGljYXRpb24gZW5kcG9pbnRzXG4gIGFzeW5jIGxvZ2luKGNyZWRlbnRpYWxzOiBMb2dpbkNyZWRlbnRpYWxzKTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xuICAgIGZvcm1EYXRhLmFwcGVuZCgndXNlcm5hbWUnLCBjcmVkZW50aWFscy51c2VybmFtZSk7XG4gICAgZm9ybURhdGEuYXBwZW5kKCdwYXNzd29yZCcsIGNyZWRlbnRpYWxzLnBhc3N3b3JkKTtcbiAgICBcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3QoJy9hdXRoL2xvZ2luJywgZm9ybURhdGEsIHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQnLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIHJlZ2lzdGVyKGRhdGE6IFJlZ2lzdGVyRGF0YSk6IFByb21pc2U8VXNlcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdCgnL2F1dGgvcmVnaXN0ZXInLCBkYXRhKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIGdldEN1cnJlbnRVc2VyKCk6IFByb21pc2U8VXNlcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KCcvYXV0aC9tZScpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0VXNlcnMoKTogUHJvbWlzZTxVc2VyW10+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldCgnL2F1dGgvdXNlcnMnKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIC8vIFByb2plY3QgZW5kcG9pbnRzXG4gIGFzeW5jIGdldFByb2plY3RzKCk6IFByb21pc2U8UHJvamVjdFtdPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoJy9wcm9qZWN0cycpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgY3JlYXRlUHJvamVjdChwcm9qZWN0RGF0YTogeyBuYW1lOiBzdHJpbmc7IGRlc2NyaXB0aW9uPzogc3RyaW5nOyBjb25maWc/OiBhbnkgfSk6IFByb21pc2U8UHJvamVjdD4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdCgnL3Byb2plY3RzJywgcHJvamVjdERhdGEpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0UHJvamVjdChpZDogbnVtYmVyKTogUHJvbWlzZTxQcm9qZWN0PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC9wcm9qZWN0cy8ke2lkfWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgdXBkYXRlUHJvamVjdChpZDogbnVtYmVyLCBkYXRhOiBQYXJ0aWFsPFByb2plY3Q+KTogUHJvbWlzZTxQcm9qZWN0PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5wdXQoYC9wcm9qZWN0cy8ke2lkfWAsIGRhdGEpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZGVsZXRlUHJvamVjdChpZDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgYXdhaXQgdGhpcy5jbGllbnQuZGVsZXRlKGAvcHJvamVjdHMvJHtpZH1gKTtcbiAgfVxuXG4gIGFzeW5jIGdldFByb2plY3RTdGF0cyhpZDogbnVtYmVyKTogUHJvbWlzZTxQcm9qZWN0U3RhdHM+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL3Byb2plY3RzLyR7aWR9L3N0YXRzYCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICAvLyBWb2x1bWUgUHJvZmlsZSBXYXZlIGVuZHBvaW50c1xuICBhc3luYyBjcmVhdGVWb2x1bWVXYXZlKGRhdGE6IENyZWF0ZVZvbHVtZVdhdmVSZXF1ZXN0KTogUHJvbWlzZTxWb2x1bWVQcm9maWxlV2F2ZT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdCgnL3ZvbHVtZS13YXZlcycsIGRhdGEpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0Vm9sdW1lV2F2ZXMoXG4gICAgcHJvamVjdElkOiBudW1iZXIsIFxuICAgIGluY2x1ZGVDb21wbGV0ZWQ6IGJvb2xlYW4gPSB0cnVlXG4gICk6IFByb21pc2U8Vm9sdW1lUHJvZmlsZVdhdmVbXT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KCcvdm9sdW1lLXdhdmVzJywge1xuICAgICAgcGFyYW1zOiB7IHByb2plY3RfaWQ6IHByb2plY3RJZCwgaW5jbHVkZV9jb21wbGV0ZWQ6IGluY2x1ZGVDb21wbGV0ZWQgfVxuICAgIH0pO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0Vm9sdW1lV2F2ZShpZDogbnVtYmVyKTogUHJvbWlzZTxXYXZlV2l0aERpc3RyaWJ1dGlvbj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAvdm9sdW1lLXdhdmVzLyR7aWR9YCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyB1cGRhdGVXYXZlQ29tcGxldGlvbihcbiAgICBpZDogbnVtYmVyLCBcbiAgICBkYXRhOiBVcGRhdGVXYXZlQ29tcGxldGlvblJlcXVlc3RcbiAgKTogUHJvbWlzZTxWb2x1bWVQcm9maWxlV2F2ZT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucHV0KGAvdm9sdW1lLXdhdmVzLyR7aWR9L2NvbXBsZXRpb25gLCBkYXRhKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIGdldEFjdGl2ZVdhdmVzKHByb2plY3RJZDogbnVtYmVyKTogUHJvbWlzZTxWb2x1bWVQcm9maWxlV2F2ZVtdPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC92b2x1bWUtd2F2ZXMvcHJvamVjdHMvJHtwcm9qZWN0SWR9L2FjdGl2ZWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0Q29tcGxldGVkV2F2ZXMocHJvamVjdElkOiBudW1iZXIsIGxpbWl0OiBudW1iZXIgPSAxMDApOiBQcm9taXNlPFZvbHVtZVByb2ZpbGVXYXZlW10+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL3ZvbHVtZS13YXZlcy9wcm9qZWN0cy8ke3Byb2plY3RJZH0vY29tcGxldGVkYCwge1xuICAgICAgcGFyYW1zOiB7IGxpbWl0IH1cbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIGdldFdhdmVTdWNjZXNzUmF0ZShcbiAgICBwcm9qZWN0SWQ6IG51bWJlciwgXG4gICAgbWluaW11bUFjY3VyYWN5OiBudW1iZXIgPSA3MFxuICApOiBQcm9taXNlPFdhdmVTdWNjZXNzU3RhdHM+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL3ZvbHVtZS13YXZlcy9wcm9qZWN0cy8ke3Byb2plY3RJZH0vc3VjY2Vzcy1yYXRlYCwge1xuICAgICAgcGFyYW1zOiB7IG1pbmltdW1fYWNjdXJhY3k6IG1pbmltdW1BY2N1cmFjeSB9XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBleHBvcnRUcmFpbmluZ0RhdGEocHJvamVjdElkOiBudW1iZXIpOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAvdm9sdW1lLXdhdmVzL3Byb2plY3RzLyR7cHJvamVjdElkfS90cmFpbmluZy1kYXRhYCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBkZWxldGVWb2x1bWVXYXZlKGlkOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBhd2FpdCB0aGlzLmNsaWVudC5kZWxldGUoYC92b2x1bWUtd2F2ZXMvJHtpZH1gKTtcbiAgfVxuXG4gIGFzeW5jIHZhbGlkYXRlV2F2ZUZvcm11bGEoaWQ6IG51bWJlcik6IFByb21pc2U8e1xuICAgIHdhdmVfaWQ6IG51bWJlcjtcbiAgICBzdGFydF9wcmljZTogbnVtYmVyO1xuICAgIHBvY19wcmljZTogbnVtYmVyO1xuICAgIGNhbGN1bGF0ZWRfdGFyZ2V0OiBudW1iZXI7XG4gICAgZXhwZWN0ZWRfdGFyZ2V0OiBudW1iZXI7XG4gICAgZm9ybXVsYTogc3RyaW5nO1xuICAgIG1hdGNoZXNfZm9ybXVsYTogYm9vbGVhbjtcbiAgICBkaWZmZXJlbmNlOiBudW1iZXI7XG4gIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL3ZvbHVtZS13YXZlcy8ke2lkfS9mb3JtdWxhLXZhbGlkYXRpb25gKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIC8vIEFubm90YXRpb24gZW5kcG9pbnRzXG4gIGFzeW5jIGNyZWF0ZUFubm90YXRpb24oZGF0YTogQ3JlYXRlQW5ub3RhdGlvblJlcXVlc3QpOiBQcm9taXNlPEFubm90YXRpb24+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3QoJy9hbm5vdGF0aW9ucycsIGRhdGEpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0QW5ub3RhdGlvbnMoXG4gICAgcHJvamVjdElkOiBudW1iZXIsXG4gICAgYW5ub3RhdGlvblR5cGU/OiBzdHJpbmcsXG4gICAgdmFsaWRhdGlvblN0YXR1cz86IHN0cmluZ1xuICApOiBQcm9taXNlPEFubm90YXRpb25bXT4ge1xuICAgIGNvbnN0IHBhcmFtczogYW55ID0geyBwcm9qZWN0X2lkOiBwcm9qZWN0SWQgfTtcbiAgICBpZiAoYW5ub3RhdGlvblR5cGUpIHBhcmFtcy5hbm5vdGF0aW9uX3R5cGUgPSBhbm5vdGF0aW9uVHlwZTtcbiAgICBpZiAodmFsaWRhdGlvblN0YXR1cykgcGFyYW1zLnZhbGlkYXRpb25fc3RhdHVzID0gdmFsaWRhdGlvblN0YXR1cztcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KCcvYW5ub3RhdGlvbnMnLCB7IHBhcmFtcyB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIGdldEFubm90YXRpb24oaWQ6IG51bWJlcik6IFByb21pc2U8QW5ub3RhdGlvbj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAvYW5ub3RhdGlvbnMvJHtpZH1gKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZUFubm90YXRpb24oaWQ6IG51bWJlciwgZGF0YTogVXBkYXRlQW5ub3RhdGlvblJlcXVlc3QpOiBQcm9taXNlPEFubm90YXRpb24+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LnB1dChgL2Fubm90YXRpb25zLyR7aWR9YCwgZGF0YSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBkZWxldGVBbm5vdGF0aW9uKGlkOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBhd2FpdCB0aGlzLmNsaWVudC5kZWxldGUoYC9hbm5vdGF0aW9ucy8ke2lkfWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0QW5ub3RhdGlvblN1bW1hcnkocHJvamVjdElkOiBudW1iZXIpOiBQcm9taXNlPEFubm90YXRpb25TdW1tYXJ5PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC9hbm5vdGF0aW9ucy9wcm9qZWN0cy8ke3Byb2plY3RJZH0vc3VtbWFyeWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgYWRkUGF0dGVybk1ldHJpY3MoYW5ub3RhdGlvbklkOiBudW1iZXIsIG1ldHJpY3M6IGFueVtdKTogUHJvbWlzZTxhbnlbXT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdChgL2Fubm90YXRpb25zLyR7YW5ub3RhdGlvbklkfS9tZXRyaWNzYCwgbWV0cmljcyk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBnZXRBbm5vdGF0aW9uTWV0cmljcyhhbm5vdGF0aW9uSWQ6IG51bWJlcik6IFByb21pc2U8YW55W10+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL2Fubm90YXRpb25zLyR7YW5ub3RhdGlvbklkfS9tZXRyaWNzYCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICAvLyBDdXN0b20gcmVxdWVzdCBtZXRob2QgZm9yIGFkZGl0aW9uYWwgZmxleGliaWxpdHlcbiAgYXN5bmMgcmVxdWVzdDxUPihjb25maWc6IEF4aW9zUmVxdWVzdENvbmZpZyk6IFByb21pc2U8VD4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucmVxdWVzdChjb25maWcpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KCk7XG5leHBvcnQgZGVmYXVsdCBhcGlDbGllbnQ7Il0sIm5hbWVzIjpbImF4aW9zIiwiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCIsIkFQSV9WRVJTSU9OIiwiQXBpQ2xpZW50IiwiY29uc3RydWN0b3IiLCJjbGllbnQiLCJjcmVhdGUiLCJiYXNlVVJMIiwiaGVhZGVycyIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJBdXRob3JpemF0aW9uIiwicmVzcG9uc2UiLCJlcnJvciIsInN0YXR1cyIsInJlbW92ZUl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJQcm9taXNlIiwicmVqZWN0IiwibG9naW4iLCJjcmVkZW50aWFscyIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJhcHBlbmQiLCJ1c2VybmFtZSIsInBhc3N3b3JkIiwicG9zdCIsImRhdGEiLCJyZWdpc3RlciIsImdldEN1cnJlbnRVc2VyIiwiZ2V0IiwiZ2V0VXNlcnMiLCJnZXRQcm9qZWN0cyIsImNyZWF0ZVByb2plY3QiLCJwcm9qZWN0RGF0YSIsImdldFByb2plY3QiLCJpZCIsInVwZGF0ZVByb2plY3QiLCJwdXQiLCJkZWxldGVQcm9qZWN0IiwiZGVsZXRlIiwiZ2V0UHJvamVjdFN0YXRzIiwiY3JlYXRlVm9sdW1lV2F2ZSIsImdldFZvbHVtZVdhdmVzIiwicHJvamVjdElkIiwiaW5jbHVkZUNvbXBsZXRlZCIsInBhcmFtcyIsInByb2plY3RfaWQiLCJpbmNsdWRlX2NvbXBsZXRlZCIsImdldFZvbHVtZVdhdmUiLCJ1cGRhdGVXYXZlQ29tcGxldGlvbiIsImdldEFjdGl2ZVdhdmVzIiwiZ2V0Q29tcGxldGVkV2F2ZXMiLCJsaW1pdCIsImdldFdhdmVTdWNjZXNzUmF0ZSIsIm1pbmltdW1BY2N1cmFjeSIsIm1pbmltdW1fYWNjdXJhY3kiLCJleHBvcnRUcmFpbmluZ0RhdGEiLCJkZWxldGVWb2x1bWVXYXZlIiwidmFsaWRhdGVXYXZlRm9ybXVsYSIsImNyZWF0ZUFubm90YXRpb24iLCJnZXRBbm5vdGF0aW9ucyIsImFubm90YXRpb25UeXBlIiwidmFsaWRhdGlvblN0YXR1cyIsImFubm90YXRpb25fdHlwZSIsInZhbGlkYXRpb25fc3RhdHVzIiwiZ2V0QW5ub3RhdGlvbiIsInVwZGF0ZUFubm90YXRpb24iLCJkZWxldGVBbm5vdGF0aW9uIiwiZ2V0QW5ub3RhdGlvblN1bW1hcnkiLCJhZGRQYXR0ZXJuTWV0cmljcyIsImFubm90YXRpb25JZCIsIm1ldHJpY3MiLCJnZXRBbm5vdGF0aW9uTWV0cmljcyIsImFwaUNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/auth-store.ts":
/*!**********************************!*\
  !*** ./src/stores/auth-store.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        login: async (credentials)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].login(credentials);\n                // Store token and user (only on client side)\n                if (false) {}\n                set({\n                    user: response.user,\n                    token: response.access_token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        register: async (data)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].register(data);\n                // After registration, automatically login\n                await get().login({\n                    username: data.username,\n                    password: data.password\n                });\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            // Remove from localStorage (only on client side)\n            if (false) {}\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        initialize: ()=>{\n            // Only run on client side\n            if (true) {\n                return;\n            }\n            const token = localStorage.getItem(\"auth_token\");\n            const userStr = localStorage.getItem(\"user\");\n            if (token && userStr) {\n                try {\n                    const user = JSON.parse(userStr);\n                    set({\n                        user,\n                        token,\n                        isAuthenticated: true,\n                        isLoading: false\n                    });\n                } catch  {\n                    // Clear corrupted data\n                    localStorage.removeItem(\"auth_token\");\n                    localStorage.removeItem(\"user\");\n                    set({\n                        user: null,\n                        token: null,\n                        isAuthenticated: false,\n                        isLoading: false\n                    });\n                }\n            } else {\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/auth-store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"adad23446b75\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ODE2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFkYWQyMzQ0NmI3NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(rsc)/./src/components/auth/auth-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"FOREX/TFEX Data Annotation Tool\",\n    description: \"Professional trading pattern analysis platform with Volume Profile Wave Analysis\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ3dDO0FBSXZELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0Msd0VBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9yZXgtYW5ub3RhdGlvbi1mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2F1dGgvYXV0aC1wcm92aWRlcidcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0ZPUkVYL1RGRVggRGF0YSBBbm5vdGF0aW9uIFRvb2wnLFxuICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCB0cmFkaW5nIHBhdHRlcm4gYW5hbHlzaXMgcGxhdGZvcm0gd2l0aCBWb2x1bWUgUHJvZmlsZSBXYXZlIEFuYWx5c2lzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth/auth-provider.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/auth-provider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Library/CloudStorage/SynologyDrive-PLoTDrive/My_Life/Stock Market/Claude Code/forex-annotation-frontend/src/components/auth/auth-provider.tsx#AuthProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/zustand","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fplotpro%2FLibrary%2FCloudStorage%2FSynologyDrive-PLoTDrive%2FMy_Life%2FStock%20Market%2FClaude%20Code%2Fforex-annotation-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();