/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 98%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --border: 214.3 31.8% 91.4%;
  --radius: 0.5rem;

  /* Trading Theme Colors */
  --trading-dark: 219 84% 9%;
  --trading-darker: 220 91% 5%;
  --trading-navy: 215 25% 27%;
  --trading-primary: 217 91% 60%;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--trading-darker);
    --foreground: 210 40% 98%;
    --card: var(--trading-dark);
    --card-foreground: 210 40% 98%;
    --muted: var(--trading-navy);
    --muted-foreground: 215 20.2% 65.1%;
    --border: var(--trading-navy);
  }
}

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  font-feature-settings: "rlig" 1, "calt" 1;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer components {
  /* Modern Card Components */
  .glass-card {
    @apply backdrop-blur-sm bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 rounded-lg shadow-lg;
  }

  .trading-card {
    @apply bg-card border border-border rounded-lg shadow-trading hover:shadow-trading-lg transition-all duration-300;
  }

  .stats-card {
    @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 
           border border-gray-200 dark:border-gray-700 rounded-xl p-6 
           hover:shadow-xl transition-all duration-300 hover:scale-105;
  }

  /* Interactive Elements */
  .btn-primary {
    @apply bg-trading-primary hover:bg-trading-accent text-white font-medium px-4 py-2 
           rounded-lg transition-all duration-200 hover:shadow-glow-blue;
  }

  .btn-success {
    @apply bg-trading-success hover:bg-bull-600 text-white font-medium px-4 py-2 
           rounded-lg transition-all duration-200 hover:shadow-glow-green;
  }

  .btn-danger {
    @apply bg-trading-danger hover:bg-bear-600 text-white font-medium px-4 py-2 
           rounded-lg transition-all duration-200 hover:shadow-glow-red;
  }

  /* Navigation Enhancements */
  .nav-item {
    @apply relative px-3 py-2 rounded-md transition-all duration-200 
           hover:bg-white/10 dark:hover:bg-white/5;
  }

  .nav-item.active::before {
    content: '';
    @apply absolute left-0 top-1/2 w-1 h-8 bg-trading-primary rounded-r-full transform -translate-y-1/2;
  }
}

@layer utilities {
  /* Text Gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-trading-primary to-trading-accent bg-clip-text text-transparent;
  }

  .text-bull-gradient {
    @apply bg-gradient-to-r from-bull-500 to-bull-600 bg-clip-text text-transparent;
  }

  .text-bear-gradient {
    @apply bg-gradient-to-r from-bear-500 to-bear-600 bg-clip-text text-transparent;
  }

  /* Status Indicators */
  .status-online {
    @apply relative;
  }

  .status-online::after {
    content: '';
    @apply absolute -top-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white 
           rounded-full animate-pulse;
  }

  /* Market Status Colors */
  .market-up {
    @apply text-bull-500 bg-bull-50 dark:bg-bull-500/10;
  }

  .market-down {
    @apply text-bear-500 bg-bear-50 dark:bg-bear-500/10;
  }

  .market-neutral {
    @apply text-gray-600 bg-gray-100 dark:bg-gray-800;
  }
}

/* Trading Chart Dark Theme */
.chart-container {
  @apply bg-trading-darker rounded-xl overflow-hidden shadow-trading-lg border border-trading-navy;
}

/* Volume Profile Wave Annotation Styles */
.wave-annotation-overlay {
  @apply relative z-10;
}

.wave-start-point {
  @apply absolute w-3 h-3 rounded-full bg-bull-500 border-2 border-white 
         transform -translate-x-1/2 -translate-y-1/2 cursor-pointer 
         hover:scale-110 transition-transform duration-200;
}

.wave-poc-line {
  @apply absolute h-0.5 bg-bear-500 opacity-80 animate-pulse-slow;
}

.wave-target-line {
  @apply absolute h-0.5 bg-trading-primary opacity-60 border-dashed;
}

/* Enhanced Scrollbars */
::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-trading-navy;
}

::-webkit-scrollbar-thumb {
  @apply bg-trading-slate rounded-full hover:bg-gray-400 transition-colors;
}

/* Performance Indicators */
.performance-indicator {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.performance-indicator.positive {
  @apply bg-bull-50 text-bull-700 dark:bg-bull-500/20 dark:text-bull-400;
}

.performance-indicator.negative {
  @apply bg-bear-50 text-bear-700 dark:bg-bear-500/20 dark:text-bear-400;
}

/* Loading States */
.skeleton {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  @apply outline-2 outline-trading-primary outline-offset-2 ring-2 ring-trading-primary/20;
}

/* Responsive Grid Utilities */
@screen sm {
  .grid-responsive {
    @apply grid-cols-2;
  }
}

@screen md {
  .grid-responsive {
    @apply grid-cols-3;
  }
}

@screen lg {
  .grid-responsive {
    @apply grid-cols-4;
  }
}

/* Animation Classes */
.fade-in {
  @apply animate-fade-in;
}

.slide-up {
  @apply animate-slide-up;
}

.scale-in {
  @apply animate-scale-in;
}

.counter-animation {
  @apply animate-counter;
}
