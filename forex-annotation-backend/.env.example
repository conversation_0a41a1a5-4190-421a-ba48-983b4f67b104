# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/forex_annotation
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=forex_annotation
DATABASE_USER=username
DATABASE_PASSWORD=password

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_VERSION=v1
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# External APIs (if needed for market data)
MARKET_DATA_API_KEY=your-market-data-api-key
MARKET_DATA_BASE_URL=https://api.marketdata.com

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json