from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime

from ..core.database import get_db
from ..models.user import User
from ..models.project import Project
from .auth import get_current_user

router = APIRouter()


# Pydantic models
class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    config: Optional[dict] = None


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[dict] = None


class ProjectResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    config: Optional[dict]
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


@router.post("/", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new project"""
    
    # Set default config if not provided
    default_config = {
        "timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"],
        "default_timeframe": "1h",
        "volume_profile_bins": 100,
        "pattern_types": [
            "volume_profile_wave",
            "cup_with_handle",
            "vcp",
            "triangle",
            "head_and_shoulders"
        ],
        "auto_save": True,
        "collaboration_enabled": True
    }
    
    config = {**default_config, **(project_data.config or {})}
    
    project = Project(
        name=project_data.name,
        description=project_data.description,
        config=config,
        user_id=current_user.id
    )
    
    db.add(project)
    db.commit()
    db.refresh(project)
    
    return project


@router.get("/", response_model=List[ProjectResponse])
async def list_projects(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100
):
    """List user's projects"""
    
    projects = db.query(Project).filter(
        Project.user_id == current_user.id
    ).offset(skip).limit(limit).all()
    
    return projects


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific project"""
    
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    return project


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: int,
    project_data: ProjectUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a project"""
    
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Update fields
    if project_data.name is not None:
        project.name = project_data.name
    
    if project_data.description is not None:
        project.description = project_data.description
    
    if project_data.config is not None:
        # Merge with existing config
        existing_config = project.config or {}
        project.config = {**existing_config, **project_data.config}
    
    db.commit()
    db.refresh(project)
    
    return project


@router.delete("/{project_id}")
async def delete_project(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a project"""
    
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    db.delete(project)
    db.commit()
    
    return {"message": "Project deleted successfully"}


@router.get("/{project_id}/stats")
async def get_project_stats(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get project statistics"""
    
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Count various entities
    from ..models.annotation import Annotation
    from ..models.volume_profile_wave import VolumeProfileWave
    from ..models.market_data import MarketData
    from sqlalchemy import func, and_
    
    # Total annotations
    total_annotations = db.query(func.count(Annotation.id)).filter(
        Annotation.project_id == project_id
    ).scalar()
    
    # Volume profile waves
    total_waves = db.query(func.count(VolumeProfileWave.id)).join(Annotation).filter(
        Annotation.project_id == project_id
    ).scalar()
    
    # Completed waves
    completed_waves = db.query(func.count(VolumeProfileWave.id)).join(Annotation).filter(
        and_(
            Annotation.project_id == project_id,
            VolumeProfileWave.actual_reached_price.isnot(None)
        )
    ).scalar()
    
    # Market data candles
    total_candles = db.query(func.count(MarketData.id)).filter(
        MarketData.project_id == project_id
    ).scalar()
    
    # Success rate for completed waves
    if completed_waves > 0:
        successful_waves = db.query(func.count(VolumeProfileWave.id)).join(Annotation).filter(
            and_(
                Annotation.project_id == project_id,
                VolumeProfileWave.accuracy_percentage >= 70
            )
        ).scalar()
        success_rate = (successful_waves / completed_waves) * 100
    else:
        success_rate = 0
    
    return {
        "project_id": project_id,
        "project_name": project.name,
        "total_annotations": total_annotations or 0,
        "volume_profile_waves": {
            "total": total_waves or 0,
            "completed": completed_waves or 0,
            "active": (total_waves or 0) - (completed_waves or 0),
            "success_rate": round(success_rate, 2)
        },
        "market_data_candles": total_candles or 0,
        "created_at": project.created_at,
        "last_updated": project.updated_at
    }