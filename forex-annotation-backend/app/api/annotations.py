from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..core.database import get_db
from ..models.user import User
from ..models.annotation import Annotation, AnnotationType, ValidationStatus
from ..models.pattern_metrics import PatternMetrics
from .auth import get_current_user

router = APIRouter()


# Pydantic models
class AnnotationCreate(BaseModel):
    project_id: int
    annotation_type: AnnotationType
    pattern_subtype: Optional[str] = None
    timestamp: datetime
    price_level: Optional[Decimal] = None
    data: dict
    confidence_level: Optional[str] = "medium"
    notes: Optional[str] = None
    tags: Optional[List[str]] = None


class AnnotationUpdate(BaseModel):
    data: Optional[dict] = None
    confidence_level: Optional[str] = None
    quality_score: Optional[Decimal] = None
    validation_status: Optional[ValidationStatus] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None


class PatternMetricResponse(BaseModel):
    id: int
    metric_name: str
    metric_value: Decimal
    metric_type: str
    metric_unit: Optional[str]
    confidence_score: Optional[Decimal]
    
    class Config:
        from_attributes = True


class AnnotationResponse(BaseModel):
    id: int
    project_id: int
    user_id: int
    annotation_type: AnnotationType
    pattern_subtype: Optional[str]
    timestamp: datetime
    price_level: Optional[Decimal]
    data: dict
    confidence_level: str
    quality_score: Optional[Decimal]
    validation_status: ValidationStatus
    notes: Optional[str]
    tags: Optional[List[str]]
    created_at: datetime
    updated_at: Optional[datetime]
    pattern_metrics: List[PatternMetricResponse] = []
    
    class Config:
        from_attributes = True


@router.post("/", response_model=AnnotationResponse)
async def create_annotation(
    annotation_data: AnnotationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new annotation"""
    
    annotation = Annotation(
        project_id=annotation_data.project_id,
        user_id=current_user.id,
        annotation_type=annotation_data.annotation_type,
        pattern_subtype=annotation_data.pattern_subtype,
        timestamp=annotation_data.timestamp,
        price_level=annotation_data.price_level,
        data=annotation_data.data,
        confidence_level=annotation_data.confidence_level or "medium",
        notes=annotation_data.notes,
        tags=annotation_data.tags
    )
    
    db.add(annotation)
    db.commit()
    db.refresh(annotation)
    
    return annotation


@router.get("/", response_model=List[AnnotationResponse])
async def list_annotations(
    project_id: int = Query(..., description="Project ID"),
    annotation_type: Optional[AnnotationType] = Query(None, description="Filter by annotation type"),
    validation_status: Optional[ValidationStatus] = Query(None, description="Filter by validation status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100
):
    """List annotations for a project"""
    
    query = db.query(Annotation).filter(Annotation.project_id == project_id)
    
    if annotation_type:
        query = query.filter(Annotation.annotation_type == annotation_type)
    
    if validation_status:
        query = query.filter(Annotation.validation_status == validation_status)
    
    annotations = query.order_by(Annotation.created_at.desc()).offset(skip).limit(limit).all()
    
    # Load pattern metrics for each annotation
    for annotation in annotations:
        metrics = db.query(PatternMetrics).filter(
            PatternMetrics.annotation_id == annotation.id
        ).all()
        annotation.pattern_metrics = metrics
    
    return annotations


@router.get("/{annotation_id}", response_model=AnnotationResponse)
async def get_annotation(
    annotation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific annotation"""
    
    annotation = db.query(Annotation).filter(Annotation.id == annotation_id).first()
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Annotation not found"
        )
    
    # Load pattern metrics
    metrics = db.query(PatternMetrics).filter(
        PatternMetrics.annotation_id == annotation.id
    ).all()
    annotation.pattern_metrics = metrics
    
    return annotation


@router.put("/{annotation_id}", response_model=AnnotationResponse)
async def update_annotation(
    annotation_id: int,
    update_data: AnnotationUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an annotation"""
    
    annotation = db.query(Annotation).filter(Annotation.id == annotation_id).first()
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Annotation not found"
        )
    
    # Check if user owns this annotation or is admin
    if annotation.user_id != current_user.id and current_user.role.value != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this annotation"
        )
    
    # Update fields
    if update_data.data is not None:
        annotation.data = update_data.data
    
    if update_data.confidence_level is not None:
        annotation.confidence_level = update_data.confidence_level
    
    if update_data.quality_score is not None:
        annotation.quality_score = update_data.quality_score
    
    if update_data.validation_status is not None:
        annotation.validation_status = update_data.validation_status
    
    if update_data.notes is not None:
        annotation.notes = update_data.notes
    
    if update_data.tags is not None:
        annotation.tags = update_data.tags
    
    db.commit()
    db.refresh(annotation)
    
    return annotation


@router.delete("/{annotation_id}")
async def delete_annotation(
    annotation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete an annotation"""
    
    annotation = db.query(Annotation).filter(Annotation.id == annotation_id).first()
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Annotation not found"
        )
    
    # Check if user owns this annotation or is admin
    if annotation.user_id != current_user.id and current_user.role.value != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this annotation"
        )
    
    db.delete(annotation)
    db.commit()
    
    return {"message": "Annotation deleted successfully"}


@router.get("/projects/{project_id}/summary")
async def get_project_annotation_summary(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get summary statistics for project annotations"""
    
    from sqlalchemy import func, and_
    
    # Count annotations by type
    type_counts = db.query(
        Annotation.annotation_type,
        func.count(Annotation.id).label('count')
    ).filter(
        Annotation.project_id == project_id
    ).group_by(Annotation.annotation_type).all()
    
    # Count by validation status
    status_counts = db.query(
        Annotation.validation_status,
        func.count(Annotation.id).label('count')
    ).filter(
        Annotation.project_id == project_id
    ).group_by(Annotation.validation_status).all()
    
    # Count by confidence level
    confidence_counts = db.query(
        Annotation.confidence_level,
        func.count(Annotation.id).label('count')
    ).filter(
        Annotation.project_id == project_id
    ).group_by(Annotation.confidence_level).all()
    
    # Average quality score
    avg_quality = db.query(func.avg(Annotation.quality_score)).filter(
        and_(
            Annotation.project_id == project_id,
            Annotation.quality_score.isnot(None)
        )
    ).scalar()
    
    # Recent activity (last 7 days)
    from datetime import timedelta
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_count = db.query(func.count(Annotation.id)).filter(
        and_(
            Annotation.project_id == project_id,
            Annotation.created_at >= week_ago
        )
    ).scalar()
    
    return {
        "project_id": project_id,
        "total_annotations": sum(count for _, count in type_counts),
        "by_type": {str(ann_type): count for ann_type, count in type_counts},
        "by_validation_status": {str(status): count for status, count in status_counts},
        "by_confidence_level": {level: count for level, count in confidence_counts},
        "average_quality_score": float(avg_quality) if avg_quality else None,
        "recent_annotations_7_days": recent_count or 0
    }


@router.post("/{annotation_id}/metrics", response_model=List[PatternMetricResponse])
async def add_pattern_metrics(
    annotation_id: int,
    metrics_data: List[dict],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add pattern metrics to an annotation"""
    
    annotation = db.query(Annotation).filter(Annotation.id == annotation_id).first()
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Annotation not found"
        )
    
    # Check ownership
    if annotation.user_id != current_user.id and current_user.role.value != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to add metrics to this annotation"
        )
    
    metrics = []
    for metric_data in metrics_data:
        metric = PatternMetrics(
            annotation_id=annotation_id,
            metric_name=metric_data.get('metric_name'),
            metric_value=Decimal(str(metric_data.get('metric_value'))),
            metric_type=metric_data.get('metric_type'),
            metric_unit=metric_data.get('metric_unit'),
            confidence_score=Decimal(str(metric_data.get('confidence_score', 1.0)))
        )
        metrics.append(metric)
    
    db.add_all(metrics)
    db.commit()
    
    for metric in metrics:
        db.refresh(metric)
    
    return metrics


@router.get("/{annotation_id}/metrics", response_model=List[PatternMetricResponse])
async def get_annotation_metrics(
    annotation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get pattern metrics for an annotation"""
    
    metrics = db.query(PatternMetrics).filter(
        PatternMetrics.annotation_id == annotation_id
    ).all()
    
    return metrics