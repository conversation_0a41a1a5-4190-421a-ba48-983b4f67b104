from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..core.database import get_db
from ..models.user import User
from ..models.volume_profile_wave import VolumeProfileWave, WaveDirection, ConfidenceLevel, ValidationStatus
from ..models.volume_profile_wave import VolumeDistribution, VolumePriceLevel
from ..services.wave_annotation_service import WaveAnnotationService
from .auth import get_current_user

router = APIRouter()


# Pydantic models
class StartPointData(BaseModel):
    timestamp: datetime
    price: Decimal


class VolumeRangeData(BaseModel):
    start_time: datetime
    end_time: datetime


class CreateVolumeWaveRequest(BaseModel):
    project_id: int
    start_point: StartPointData
    volume_range: VolumeRangeData
    timeframe: str = "1h"
    poc_override: Optional[Decimal] = None
    notes: Optional[str] = None


class UpdateWaveCompletionRequest(BaseModel):
    actual_reached_price: Decimal
    reached_at: datetime
    reversal_occurred: bool = False


class VolumeWaveResponse(BaseModel):
    id: int
    annotation_id: int
    wave_number: Optional[int]
    wave_direction: WaveDirection
    wave_strength: Optional[int]
    
    # Core components
    start_point_price: Decimal
    start_point_timestamp: datetime
    start_point_volume: Optional[Decimal]
    mode_poc_price: Decimal
    mode_poc_timestamp: Optional[datetime]
    calculated_target_price: Decimal
    
    # Completion data
    actual_reached_price: Optional[Decimal]
    accuracy_percentage: Optional[Decimal]
    reached_at: Optional[datetime]
    duration_actual: Optional[int]
    reversal_occurred: Optional[bool]
    
    # Quality metrics
    quality_score: Optional[Decimal]
    confidence_level: ConfidenceLevel
    validation_status: ValidationStatus
    
    # Metadata
    annotated_by: int
    notes: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


class VolumePriceLevelResponse(BaseModel):
    price_level: Decimal
    volume_amount: Decimal
    percentage_of_total: Optional[Decimal]
    
    class Config:
        from_attributes = True


class VolumeDistributionResponse(BaseModel):
    id: int
    timeframe: str
    calculation_start: datetime
    calculation_end: datetime
    poc_price: Decimal
    value_area_high: Optional[Decimal]
    value_area_low: Optional[Decimal]
    total_volume: Decimal
    vwap: Optional[Decimal]
    poc_dominance: Optional[Decimal]
    price_levels: List[VolumePriceLevelResponse] = []
    
    class Config:
        from_attributes = True


class WaveWithDistributionResponse(VolumeWaveResponse):
    volume_distributions: List[VolumeDistributionResponse] = []


@router.post("/", response_model=VolumeWaveResponse)
async def create_volume_wave(
    request: CreateVolumeWaveRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new Volume Profile Wave annotation"""
    
    try:
        service = WaveAnnotationService(db)
        
        wave = service.create_wave_annotation(
            project_id=request.project_id,
            user_id=current_user.id,
            start_point={
                'timestamp': request.start_point.timestamp,
                'price': request.start_point.price
            },
            volume_range={
                'start_time': request.volume_range.start_time,
                'end_time': request.volume_range.end_time
            },
            timeframe=request.timeframe,
            poc_override=request.poc_override,
            notes=request.notes
        )
        
        return wave
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create volume wave: {str(e)}"
        )


@router.get("/", response_model=List[VolumeWaveResponse])
async def list_volume_waves(
    project_id: int = Query(..., description="Project ID"),
    include_completed: bool = Query(True, description="Include completed waves"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 50
):
    """List Volume Profile Waves for a project"""
    
    service = WaveAnnotationService(db)
    
    if include_completed:
        # Get all waves
        from ..models.annotation import Annotation
        from sqlalchemy import and_, desc
        
        waves = db.query(VolumeProfileWave).join(Annotation).filter(
            Annotation.project_id == project_id
        ).order_by(desc(VolumeProfileWave.created_at)).offset(skip).limit(limit).all()
    else:
        # Get only active waves
        waves = service.get_active_waves(project_id, limit)
    
    return waves


@router.get("/{wave_id}", response_model=WaveWithDistributionResponse)
async def get_volume_wave(
    wave_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific Volume Profile Wave with volume distribution data"""
    
    wave = db.query(VolumeProfileWave).filter(VolumeProfileWave.id == wave_id).first()
    
    if not wave:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Volume wave not found"
        )
    
    # Get volume distributions with price levels
    distributions = db.query(VolumeDistribution).filter(
        VolumeDistribution.wave_id == wave_id
    ).all()
    
    # Add price levels to each distribution
    for dist in distributions:
        price_levels = db.query(VolumePriceLevel).filter(
            VolumePriceLevel.volume_distribution_id == dist.id
        ).order_by(VolumePriceLevel.volume_amount.desc()).all()
        
        dist.price_levels = [
            VolumePriceLevelResponse(
                price_level=level.price_level,
                volume_amount=level.volume_amount,
                percentage_of_total=level.percentage_of_total
            ) for level in price_levels
        ]
    
    # Create response
    wave_response = WaveWithDistributionResponse.from_orm(wave)
    wave_response.volume_distributions = [
        VolumeDistributionResponse.from_orm(dist) for dist in distributions
    ]
    
    return wave_response


@router.put("/{wave_id}/completion", response_model=VolumeWaveResponse)
async def update_wave_completion(
    wave_id: int,
    request: UpdateWaveCompletionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update wave with completion data"""
    
    try:
        service = WaveAnnotationService(db)
        
        wave = service.update_wave_completion(
            wave_id=wave_id,
            actual_reached_price=request.actual_reached_price,
            reached_at=request.reached_at,
            reversal_occurred=request.reversal_occurred
        )
        
        return wave
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/projects/{project_id}/active", response_model=List[VolumeWaveResponse])
async def get_active_waves(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get active (incomplete) waves for a project"""
    
    service = WaveAnnotationService(db)
    waves = service.get_active_waves(project_id)
    
    return waves


@router.get("/projects/{project_id}/completed", response_model=List[VolumeWaveResponse])  
async def get_completed_waves(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = Query(100, le=1000)
):
    """Get completed waves for a project"""
    
    service = WaveAnnotationService(db)
    waves = service.get_completed_waves(project_id, limit)
    
    return waves


@router.get("/projects/{project_id}/success-rate")
async def get_wave_success_rate(
    project_id: int,
    minimum_accuracy: float = Query(70.0, ge=0.0, le=100.0),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get success rate statistics for project waves"""
    
    service = WaveAnnotationService(db)
    stats = service.calculate_wave_success_rate(project_id, minimum_accuracy)
    
    return stats


@router.get("/projects/{project_id}/training-data")
async def export_training_data(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export wave data for ML training"""
    
    service = WaveAnnotationService(db)
    training_data = service.export_wave_training_data(project_id)
    
    return {
        "project_id": project_id,
        "total_waves": len(training_data),
        "data": training_data,
        "export_timestamp": datetime.utcnow().isoformat()
    }


@router.delete("/{wave_id}")
async def delete_volume_wave(
    wave_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a Volume Profile Wave"""
    
    wave = db.query(VolumeProfileWave).filter(VolumeProfileWave.id == wave_id).first()
    
    if not wave:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Volume wave not found"
        )
    
    # Check if user owns this wave
    if wave.annotated_by != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this wave"
        )
    
    # Delete volume distributions and price levels (cascade should handle this)
    db.delete(wave)
    db.commit()
    
    return {"message": "Volume wave deleted successfully"}


@router.get("/{wave_id}/formula-validation")
async def validate_wave_formula(
    wave_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Validate that the target price matches the Volume Profile Wave formula"""
    
    wave = db.query(VolumeProfileWave).filter(VolumeProfileWave.id == wave_id).first()
    
    if not wave:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Volume wave not found"
        )
    
    # Calculate expected target using formula
    expected_target = wave.target_formula_result
    actual_target = float(wave.calculated_target_price)
    
    # Check if they match (allowing for small floating point differences)
    matches_formula = abs(expected_target - actual_target) < 0.000001
    
    return {
        "wave_id": wave_id,
        "start_price": float(wave.start_point_price),
        "poc_price": float(wave.mode_poc_price),
        "calculated_target": actual_target,
        "expected_target": expected_target,
        "formula": "(POC - Start) + POC",
        "matches_formula": matches_formula,
        "difference": abs(expected_target - actual_target)
    }