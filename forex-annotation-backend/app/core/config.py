from pydantic_settings import BaseSettings
from typing import List, Optional
import secrets


class Settings(BaseSettings):
    """Application settings and configuration"""
    
    # API Configuration
    API_VERSION: str = "v1"
    PROJECT_NAME: str = "FOREX/TFEX Data Annotation Tool"
    DEBUG: bool = True
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Database
    DATABASE_URL: Optional[str] = None
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: int = 5432
    DATABASE_NAME: str = "forex_annotation"
    DATABASE_USER: str = "postgres"
    DATABASE_PASSWORD: str = "password"
    
    @property
    def database_url(self) -> str:
        if self.DATABASE_URL:
            return self.DATABASE_URL
        return f"postgresql://{self.DATABASE_USER}:{self.DATABASE_PASSWORD}@{self.DATABASE_HOST}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"
    
    @property
    def async_database_url(self) -> str:
        if self.DATABASE_URL:
            return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
        return f"postgresql+asyncpg://{self.DATABASE_USER}:{self.DATABASE_PASSWORD}@{self.DATABASE_HOST}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"
    
    # CORS
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Market Data
    MARKET_DATA_API_KEY: Optional[str] = None
    MARKET_DATA_BASE_URL: str = "https://api.example.com"
    
    # Volume Profile Settings
    DEFAULT_VOLUME_PROFILE_BINS: int = 100
    MIN_VOLUME_CALCULATION_PERIODS: int = 10
    MAX_VOLUME_CALCULATION_PERIODS: int = 500
    
    # ML Model Settings
    ML_MODEL_PATH: str = "./models"
    FEATURE_LOOKBACK_PERIODS: int = 100
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()