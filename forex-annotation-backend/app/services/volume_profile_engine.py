from typing import List, Dict, Tu<PERSON>, Optional
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
import numpy as np
from sqlalchemy.orm import Session

from ..models.market_data import MarketData
from ..models.volume_profile_wave import VolumeDistribution, VolumePriceLevel, DistributionShape
from ..core.config import settings


class VolumeProfileCalculator:
    """Core engine for Volume Profile calculations and Wave Analysis"""
    
    def __init__(self, precision: int = 6):
        self.precision = precision  # Price precision for FOREX
        self.min_periods = settings.MIN_VOLUME_CALCULATION_PERIODS
        self.max_periods = settings.MAX_VOLUME_CALCULATION_PERIODS
        
    def calculate_volume_profile(
        self, 
        market_data: List[MarketData], 
        start_time: datetime, 
        end_time: datetime,
        bins: Optional[int] = None
    ) -> Dict:
        """
        Calculate volume distribution across price levels for given time range
        
        Args:
            market_data: List of OHLCV market data
            start_time: Start of calculation period
            end_time: End of calculation period  
            bins: Number of price bins (default from settings)
            
        Returns:
            Dictionary containing volume profile data
        """
        # Filter data by time range
        filtered_data = self._filter_by_timerange(market_data, start_time, end_time)
        
        if len(filtered_data) < self.min_periods:
            raise ValueError(f"Insufficient data: minimum {self.min_periods} periods required")
            
        if len(filtered_data) > self.max_periods:
            raise ValueError(f"Too much data: maximum {self.max_periods} periods allowed")
        
        # Determine price range
        price_range = self._get_price_range(filtered_data)
        
        # Use default bins if not specified
        if bins is None:
            bins = min(settings.DEFAULT_VOLUME_PROFILE_BINS, len(filtered_data) * 2)
        
        # Create price bins
        price_bins = self._create_price_bins(price_range['low'], price_range['high'], bins)
        
        # Distribute volume across price levels
        volume_distribution = self._distribute_volume_by_price(filtered_data, price_bins)
        
        # Calculate statistics
        stats = self._calculate_volume_statistics(volume_distribution, filtered_data)
        
        return {
            'price_volume_pairs': volume_distribution,
            'statistics': stats,
            'calculation_period': {
                'start': start_time,
                'end': end_time,
                'candle_count': len(filtered_data)
            },
            'price_range': price_range
        }
    
    def identify_poc(self, volume_profile_data: Dict) -> Tuple[Decimal, Decimal]:
        """
        Find Point of Control - price level with highest volume
        
        Returns:
            Tuple of (poc_price, poc_volume)
        """
        volume_pairs = volume_profile_data['price_volume_pairs']
        
        if not volume_pairs:
            raise ValueError("No volume data available for POC calculation")
        
        max_volume = Decimal('0')
        poc_price = Decimal('0')
        
        for price_level, volume_data in volume_pairs.items():
            if volume_data['volume'] > max_volume:
                max_volume = volume_data['volume']
                poc_price = Decimal(str(price_level))
        
        return poc_price, max_volume
    
    def calculate_target_price(self, start_price: Decimal, poc_price: Decimal) -> Decimal:
        """
        Core Volume Profile Wave formula: (Mode - Start) + Mode
        
        Args:
            start_price: Wave start point price
            poc_price: Point of Control (Mode) price
            
        Returns:
            Calculated target price
        """
        distance = poc_price - start_price
        target = poc_price + distance
        
        # Round to appropriate precision
        return target.quantize(Decimal(10) ** -self.precision, rounding=ROUND_HALF_UP)
    
    def validate_wave_criteria(
        self, 
        start_price: Decimal, 
        poc_price: Decimal, 
        volume_profile_data: Dict
    ) -> Dict:
        """
        Validate if the start point, POC, and volume distribution form a quality wave
        
        Returns:
            Dictionary with validation results and quality scores
        """
        # Calculate basic metrics
        price_distance = abs(poc_price - start_price)
        distance_ratio = float(price_distance / start_price)
        
        # POC volume concentration
        poc_concentration = self._calculate_poc_concentration(volume_profile_data, poc_price)
        
        # Volume distribution analysis
        distribution_quality = self._analyze_distribution_quality(volume_profile_data)
        
        # Validation criteria
        criteria = {
            'sufficient_distance': distance_ratio >= 0.001,  # Min 0.1% price distance
            'poc_dominance': poc_concentration >= 0.15,      # POC has 15%+ of volume
            'clean_distribution': distribution_quality['is_clean'],
            'adequate_volume': volume_profile_data['statistics']['total_volume'] > 1000
        }
        
        # Calculate overall quality score (0.0 - 1.0)
        quality_score = self._calculate_quality_score(
            distance_ratio, poc_concentration, distribution_quality, criteria
        )
        
        return {
            'criteria': criteria,
            'metrics': {
                'distance_ratio': distance_ratio,
                'poc_concentration': poc_concentration,
                'quality_score': quality_score,
                'distribution_shape': distribution_quality['shape']
            },
            'recommendations': self._generate_recommendations(criteria, quality_score)
        }
    
    def create_volume_distribution_model(
        self, 
        wave_id: int, 
        volume_profile_data: Dict,
        timeframe: str
    ) -> VolumeDistribution:
        """Create VolumeDistribution model from calculated data"""
        
        stats = volume_profile_data['statistics']
        period = volume_profile_data['calculation_period']
        
        distribution = VolumeDistribution(
            wave_id=wave_id,
            timeframe=timeframe,
            calculation_start=period['start'],
            calculation_end=period['end'],
            poc_price=stats['poc_price'],
            value_area_high=stats['value_area_high'],
            value_area_low=stats['value_area_low'],
            total_volume=stats['total_volume'],
            price_range=stats['price_range'],
            vwap=stats['vwap'],
            distribution_shape=self._determine_distribution_shape(volume_profile_data),
            poc_dominance=stats['poc_dominance']
        )
        
        return distribution
    
    def create_volume_price_levels(
        self, 
        distribution_id: int,
        volume_profile_data: Dict
    ) -> List[VolumePriceLevel]:
        """Create VolumePriceLevel models from volume distribution data"""
        
        price_levels = []
        total_volume = volume_profile_data['statistics']['total_volume']
        
        for price_level, volume_data in volume_profile_data['price_volume_pairs'].items():
            percentage = float(volume_data['volume'] / total_volume * 100)
            
            level = VolumePriceLevel(
                volume_distribution_id=distribution_id,
                price_level=Decimal(str(price_level)),
                volume_amount=volume_data['volume'],
                candle_count=volume_data['candle_count'],
                percentage_of_total=Decimal(str(percentage)).quantize(
                    Decimal('0.01'), rounding=ROUND_HALF_UP
                )
            )
            price_levels.append(level)
        
        return price_levels
    
    # Private helper methods
    
    def _filter_by_timerange(
        self, 
        market_data: List[MarketData], 
        start_time: datetime, 
        end_time: datetime
    ) -> List[MarketData]:
        """Filter market data by time range"""
        return [
            candle for candle in market_data
            if start_time <= candle.timestamp <= end_time
        ]
    
    def _get_price_range(self, market_data: List[MarketData]) -> Dict:
        """Get price range from market data"""
        highs = [float(candle.high_price) for candle in market_data]
        lows = [float(candle.low_price) for candle in market_data]
        
        return {
            'high': max(highs),
            'low': min(lows),
            'range': max(highs) - min(lows)
        }
    
    def _create_price_bins(self, low: float, high: float, bins: int) -> List[float]:
        """Create price bins for volume distribution"""
        return np.linspace(low, high, bins + 1).tolist()
    
    def _distribute_volume_by_price(
        self, 
        market_data: List[MarketData], 
        price_bins: List[float]
    ) -> Dict:
        """Distribute volume across price levels"""
        
        volume_distribution = {}
        
        # Initialize bins
        for i in range(len(price_bins) - 1):
            price_level = (price_bins[i] + price_bins[i + 1]) / 2
            volume_distribution[price_level] = {
                'volume': Decimal('0'),
                'candle_count': 0
            }
        
        # Distribute volume from each candle
        for candle in market_data:
            candle_range = self._get_price_levels_in_candle(candle)
            volume_per_level = candle.volume / len(candle_range)
            
            for price in candle_range:
                # Find appropriate bin
                bin_price = self._find_price_bin(price, price_bins, volume_distribution.keys())
                if bin_price:
                    volume_distribution[bin_price]['volume'] += volume_per_level
                    volume_distribution[bin_price]['candle_count'] += 1
        
        return volume_distribution
    
    def _get_price_levels_in_candle(self, candle: MarketData) -> List[float]:
        """Get price levels within a candle's range"""
        # Simplified: assume uniform distribution across OHLC range
        prices = [
            float(candle.open_price),
            float(candle.high_price), 
            float(candle.low_price),
            float(candle.close_price)
        ]
        
        # Create more granular levels within the range
        min_price = min(prices)
        max_price = max(prices)
        
        if max_price == min_price:
            return [min_price]
        
        # Create levels with appropriate granularity
        step = (max_price - min_price) / 10  # 10 levels per candle
        levels = []
        
        current_price = min_price
        while current_price <= max_price:
            levels.append(current_price)
            current_price += step
            
        return levels
    
    def _find_price_bin(
        self, 
        price: float, 
        price_bins: List[float], 
        distribution_keys: List[float]
    ) -> Optional[float]:
        """Find the appropriate price bin for a given price"""
        
        # Find closest bin
        closest_bin = None
        min_distance = float('inf')
        
        for bin_price in distribution_keys:
            distance = abs(price - bin_price)
            if distance < min_distance:
                min_distance = distance
                closest_bin = bin_price
        
        return closest_bin
    
    def _calculate_volume_statistics(
        self, 
        volume_distribution: Dict,
        market_data: List[MarketData]
    ) -> Dict:
        """Calculate comprehensive volume statistics"""
        
        total_volume = sum(data['volume'] for data in volume_distribution.values())
        
        # Find POC
        poc_price, poc_volume = self.identify_poc({'price_volume_pairs': volume_distribution})
        poc_dominance = float(poc_volume / total_volume * 100)
        
        # Calculate VWAP
        total_price_volume = sum(
            Decimal(str(price)) * data['volume'] 
            for price, data in volume_distribution.items()
        )
        vwap = total_price_volume / total_volume if total_volume > 0 else Decimal('0')
        
        # Calculate value area (70% of volume)
        value_area = self._calculate_value_area(volume_distribution, total_volume)
        
        # Price range
        prices = [float(candle.high_price) for candle in market_data] + \
                [float(candle.low_price) for candle in market_data]
        price_range = max(prices) - min(prices)
        
        return {
            'poc_price': poc_price,
            'poc_volume': poc_volume,
            'poc_dominance': Decimal(str(poc_dominance)).quantize(
                Decimal('0.01'), rounding=ROUND_HALF_UP
            ),
            'total_volume': total_volume,
            'vwap': vwap,
            'value_area_high': value_area['high'],
            'value_area_low': value_area['low'],
            'price_range': Decimal(str(price_range))
        }
    
    def _calculate_value_area(self, volume_distribution: Dict, total_volume: Decimal) -> Dict:
        """Calculate 70% value area boundaries"""
        
        # Sort by volume descending
        sorted_levels = sorted(
            volume_distribution.items(),
            key=lambda x: x[1]['volume'],
            reverse=True
        )
        
        target_volume = total_volume * Decimal('0.7')  # 70%
        accumulated_volume = Decimal('0')
        value_area_prices = []
        
        for price, data in sorted_levels:
            accumulated_volume += data['volume']
            value_area_prices.append(price)
            
            if accumulated_volume >= target_volume:
                break
        
        if value_area_prices:
            return {
                'high': Decimal(str(max(value_area_prices))),
                'low': Decimal(str(min(value_area_prices)))
            }
        else:
            return {'high': Decimal('0'), 'low': Decimal('0')}
    
    def _calculate_poc_concentration(
        self, 
        volume_profile_data: Dict, 
        poc_price: Decimal
    ) -> float:
        """Calculate POC volume concentration percentage"""
        
        price_volume_pairs = volume_profile_data['price_volume_pairs']
        total_volume = volume_profile_data['statistics']['total_volume']
        
        poc_volume = Decimal('0')
        poc_price_float = float(poc_price)
        
        # Find the exact POC volume
        for price, data in price_volume_pairs.items():
            if abs(price - poc_price_float) < 0.0001:  # Allow for small floating point differences
                poc_volume = data['volume']
                break
        
        if total_volume > 0:
            return float(poc_volume / total_volume)
        return 0.0
    
    def _analyze_distribution_quality(self, volume_profile_data: Dict) -> Dict:
        """Analyze the quality of volume distribution"""
        
        volumes = [data['volume'] for data in volume_profile_data['price_volume_pairs'].values()]
        volumes_array = np.array([float(v) for v in volumes])
        
        # Calculate distribution metrics
        mean_volume = np.mean(volumes_array)
        std_volume = np.std(volumes_array)
        cv = std_volume / mean_volume if mean_volume > 0 else 0  # Coefficient of variation
        
        # Determine if distribution is clean (low noise, clear POC)
        is_clean = cv > 0.5  # Higher variation indicates clearer POC
        
        shape = self._determine_distribution_shape({'price_volume_pairs': volume_profile_data['price_volume_pairs']})
        
        return {
            'is_clean': is_clean,
            'coefficient_of_variation': cv,
            'shape': shape,
            'volume_std': std_volume
        }
    
    def _determine_distribution_shape(self, volume_profile_data: Dict) -> DistributionShape:
        """Determine the shape of volume distribution"""
        
        volumes = [data['volume'] for data in volume_profile_data['price_volume_pairs'].values()]
        prices = list(volume_profile_data['price_volume_pairs'].keys())
        
        if len(volumes) < 3:
            return DistributionShape.NORMAL
        
        volumes_array = np.array([float(v) for v in volumes])
        
        # Find peaks
        peaks = []
        for i in range(1, len(volumes_array) - 1):
            if volumes_array[i] > volumes_array[i-1] and volumes_array[i] > volumes_array[i+1]:
                peaks.append(i)
        
        if len(peaks) >= 2:
            return DistributionShape.BIMODAL
        elif len(peaks) == 1:
            peak_index = peaks[0]
            if peak_index < len(volumes_array) * 0.3:
                return DistributionShape.SKEWED_LOW
            elif peak_index > len(volumes_array) * 0.7:
                return DistributionShape.SKEWED_HIGH
        
        return DistributionShape.NORMAL
    
    def _calculate_quality_score(
        self, 
        distance_ratio: float, 
        poc_concentration: float, 
        distribution_quality: Dict,
        criteria: Dict
    ) -> float:
        """Calculate overall quality score (0.0 - 1.0)"""
        
        score = 0.0
        
        # Distance score (0-0.3)
        distance_score = min(distance_ratio * 100, 0.3)  # Cap at 0.3
        score += distance_score
        
        # POC concentration score (0-0.4)  
        poc_score = min(poc_concentration * 2, 0.4)  # Cap at 0.4
        score += poc_score
        
        # Distribution quality score (0-0.2)
        if distribution_quality['is_clean']:
            score += 0.2
        
        # Criteria bonus (0-0.1)
        passed_criteria = sum(1 for passed in criteria.values() if passed)
        criteria_score = (passed_criteria / len(criteria)) * 0.1
        score += criteria_score
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _generate_recommendations(self, criteria: Dict, quality_score: float) -> List[str]:
        """Generate recommendations for improving wave quality"""
        
        recommendations = []
        
        if not criteria['sufficient_distance']:
            recommendations.append("Increase distance between start point and POC for better wave clarity")
        
        if not criteria['poc_dominance']:
            recommendations.append("Look for stronger POC with higher volume concentration (>15%)")
        
        if not criteria['clean_distribution']:
            recommendations.append("Find cleaner volume distribution with clearer POC dominance")
        
        if quality_score < 0.5:
            recommendations.append("Consider finding a different start point or POC for higher quality wave")
        elif quality_score < 0.7:
            recommendations.append("Good wave setup, monitor for completion")
        else:
            recommendations.append("Excellent wave setup with high probability of success")
        
        return recommendations