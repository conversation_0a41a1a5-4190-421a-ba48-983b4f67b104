from typing import List, Dict, Optional, Tuple
from datetime import datetime
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from ..models.annotation import Annotation, AnnotationType, ValidationStatus
from ..models.volume_profile_wave import VolumeProfileWave, WaveDirection, ConfidenceLevel
from ..models.market_data import MarketData
from ..models.pattern_metrics import PatternMetrics
from .volume_profile_engine import VolumeProfileCalculator


class WaveAnnotationService:
    """Service for managing Volume Profile Wave annotations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.volume_calculator = VolumeProfileCalculator()
    
    def create_wave_annotation(
        self,
        project_id: int,
        user_id: int,
        start_point: Dict,
        volume_range: Dict,
        timeframe: str,
        poc_override: Optional[Decimal] = None,
        notes: Optional[str] = None
    ) -> VolumeProfileWave:
        """
        Create a complete Volume Profile Wave annotation
        
        Args:
            project_id: Project ID
            user_id: User creating the annotation
            start_point: {'timestamp': datetime, 'price': Decimal}
            volume_range: {'start_time': datetime, 'end_time': datetime}
            timeframe: Chart timeframe (1m, 5m, etc.)
            poc_override: Manual POC price override
            notes: Optional notes
            
        Returns:
            Created VolumeProfileWave instance
        """
        
        # Get market data for volume calculation
        market_data = self._get_market_data_for_range(
            project_id, 
            volume_range['start_time'], 
            volume_range['end_time'],
            timeframe
        )
        
        if not market_data:
            raise ValueError("No market data found for the specified range")
        
        # Calculate volume profile
        volume_profile_data = self.volume_calculator.calculate_volume_profile(
            market_data,
            volume_range['start_time'],
            volume_range['end_time']
        )
        
        # Identify POC (use override if provided)
        if poc_override:
            poc_price = poc_override
        else:
            poc_price, _ = self.volume_calculator.identify_poc(volume_profile_data)
        
        # Calculate target using formula: (POC - Start) + POC
        target_price = self.volume_calculator.calculate_target_price(
            start_point['price'], poc_price
        )
        
        # Validate wave quality
        validation_results = self.volume_calculator.validate_wave_criteria(
            start_point['price'], poc_price, volume_profile_data
        )
        
        # Determine wave direction
        wave_direction = WaveDirection.BULLISH if target_price > poc_price else WaveDirection.BEARISH
        
        # Create base annotation
        annotation = Annotation(
            project_id=project_id,
            user_id=user_id,
            annotation_type=AnnotationType.VOLUME_PROFILE_WAVE,
            timestamp=start_point['timestamp'],
            price_level=start_point['price'],
            data={
                'start_point': {
                    'price': float(start_point['price']),
                    'timestamp': start_point['timestamp'].isoformat()
                },
                'volume_calculation_range': {
                    'start_time': volume_range['start_time'].isoformat(),
                    'end_time': volume_range['end_time'].isoformat()
                },
                'poc_price': float(poc_price),
                'target_price': float(target_price),
                'formula_used': '(POC - Start) + POC'
            },
            quality_score=Decimal(str(validation_results['metrics']['quality_score'])),
            confidence_level=self._determine_confidence_level(validation_results['metrics']['quality_score']),
            notes=notes
        )
        
        self.db.add(annotation)
        self.db.flush()  # Get annotation ID
        
        # Create Volume Profile Wave
        wave = VolumeProfileWave(
            annotation_id=annotation.id,
            wave_number=self._get_next_wave_number(project_id),
            wave_direction=wave_direction,
            wave_strength=self._calculate_wave_strength(validation_results),
            
            # Start point
            start_point_price=start_point['price'],
            start_point_timestamp=start_point['timestamp'],
            start_point_volume=self._get_volume_at_timestamp(market_data, start_point['timestamp']),
            selection_reason=f"Volume analysis: {validation_results['recommendations'][0] if validation_results['recommendations'] else 'Manual selection'}",
            
            # POC/Mode
            mode_poc_price=poc_price,
            mode_poc_timestamp=self._estimate_poc_timestamp(market_data, poc_price),
            volume_density=volume_profile_data['statistics']['poc_volume'],
            total_volume_percentage=validation_results['metrics']['poc_concentration'] * 100,
            
            # Target
            calculated_target_price=target_price,
            
            # Quality metrics
            prediction_accuracy=None,  # Will be updated when wave completes
            quality_score=Decimal(str(validation_results['metrics']['quality_score'])),
            confidence_level=self._determine_confidence_level(validation_results['metrics']['quality_score']),
            validation_status=ValidationStatus.PENDING,
            
            # User
            annotated_by=user_id,
            notes=notes
        )
        
        self.db.add(wave)
        self.db.flush()  # Get wave ID
        
        # Create volume distribution records
        volume_distribution = self.volume_calculator.create_volume_distribution_model(
            wave.id, volume_profile_data, timeframe
        )
        self.db.add(volume_distribution)
        self.db.flush()
        
        # Create volume price levels
        price_levels = self.volume_calculator.create_volume_price_levels(
            volume_distribution.id, volume_profile_data
        )
        self.db.add_all(price_levels)
        
        # Create pattern metrics
        metrics = PatternMetrics.create_volume_wave_metrics(
            annotation.id,
            {
                'start_poc_distance': float(abs(poc_price - start_point['price']) * 10000),  # In pips
                'poc_concentration_percentage': validation_results['metrics']['poc_concentration'] * 100,
                'accuracy_percentage': None,  # Will update when completed
                'duration_minutes': None  # Will update when completed
            }
        )
        self.db.add_all(metrics)
        
        self.db.commit()
        
        return wave
    
    def update_wave_completion(
        self,
        wave_id: int,
        actual_reached_price: Decimal,
        reached_at: datetime,
        reversal_occurred: bool = False
    ) -> VolumeProfileWave:
        """Update wave with completion data"""
        
        wave = self.db.query(VolumeProfileWave).filter(
            VolumeProfileWave.id == wave_id
        ).first()
        
        if not wave:
            raise ValueError(f"Wave with ID {wave_id} not found")
        
        # Calculate accuracy
        target_price = wave.calculated_target_price
        price_difference = abs(actual_reached_price - target_price)
        accuracy_percentage = max(0, 100 - (float(price_difference / target_price) * 100))
        
        # Calculate duration
        duration_minutes = int((reached_at - wave.start_point_timestamp).total_seconds() / 60)
        
        # Update wave
        wave.actual_reached_price = actual_reached_price
        wave.reached_at = reached_at
        wave.accuracy_percentage = Decimal(str(accuracy_percentage)).quantize(Decimal('0.01'))
        wave.prediction_accuracy = wave.accuracy_percentage
        wave.duration_actual = duration_minutes
        wave.reversal_occurred = reversal_occurred
        
        # Update validation status based on accuracy
        if accuracy_percentage >= 80:
            wave.validation_status = ValidationStatus.CONFIRMED
        elif accuracy_percentage >= 50:
            wave.validation_status = ValidationStatus.NEEDS_REVIEW
        else:
            wave.validation_status = ValidationStatus.REJECTED
        
        # Update related metrics
        self._update_wave_metrics(wave.annotation_id, accuracy_percentage, duration_minutes)
        
        self.db.commit()
        
        # If reversal occurred, create suggestion for next wave
        if reversal_occurred:
            self._suggest_next_wave(wave, actual_reached_price, reached_at)
        
        return wave
    
    def get_active_waves(self, project_id: int, limit: int = 50) -> List[VolumeProfileWave]:
        """Get active (incomplete) waves for a project"""
        
        return self.db.query(VolumeProfileWave).join(Annotation).filter(
            and_(
                Annotation.project_id == project_id,
                VolumeProfileWave.actual_reached_price.is_(None)
            )
        ).order_by(desc(VolumeProfileWave.created_at)).limit(limit).all()
    
    def get_completed_waves(self, project_id: int, limit: int = 100) -> List[VolumeProfileWave]:
        """Get completed waves for analysis"""
        
        return self.db.query(VolumeProfileWave).join(Annotation).filter(
            and_(
                Annotation.project_id == project_id,
                VolumeProfileWave.actual_reached_price.isnot(None)
            )
        ).order_by(desc(VolumeProfileWave.reached_at)).limit(limit).all()
    
    def calculate_wave_success_rate(self, project_id: int, minimum_accuracy: float = 70.0) -> Dict:
        """Calculate success rate statistics for waves"""
        
        completed_waves = self.get_completed_waves(project_id, limit=1000)
        
        if not completed_waves:
            return {
                'total_waves': 0,
                'success_rate': 0.0,
                'average_accuracy': 0.0,
                'average_duration_hours': 0.0
            }
        
        successful_waves = [
            wave for wave in completed_waves 
            if wave.accuracy_percentage and float(wave.accuracy_percentage) >= minimum_accuracy
        ]
        
        success_rate = len(successful_waves) / len(completed_waves) * 100
        
        average_accuracy = sum(
            float(wave.accuracy_percentage) for wave in completed_waves
            if wave.accuracy_percentage
        ) / len(completed_waves)
        
        average_duration = sum(
            wave.duration_actual for wave in completed_waves
            if wave.duration_actual
        ) / len(completed_waves) / 60  # Convert to hours
        
        return {
            'total_waves': len(completed_waves),
            'successful_waves': len(successful_waves),
            'success_rate': round(success_rate, 2),
            'average_accuracy': round(average_accuracy, 2),
            'average_duration_hours': round(average_duration, 2)
        }
    
    def export_wave_training_data(self, project_id: int) -> List[Dict]:
        """Export waves in format suitable for ML training"""
        
        completed_waves = self.get_completed_waves(project_id, limit=10000)
        training_data = []
        
        for wave in completed_waves:
            if not wave.accuracy_percentage:
                continue
            
            wave_data = {
                # Features (inputs for ML)
                'start_price': float(wave.start_point_price),
                'poc_price': float(wave.mode_poc_price),
                'target_price': float(wave.calculated_target_price),
                'poc_volume_percentage': float(wave.total_volume_percentage or 0),
                'quality_score': float(wave.quality_score or 0),
                'wave_direction': wave.wave_direction.value,
                'wave_strength': wave.wave_strength or 0,
                
                # Labels (outputs for ML)
                'success': 1 if float(wave.accuracy_percentage) >= 70 else 0,
                'accuracy_percentage': float(wave.accuracy_percentage),
                'duration_hours': (wave.duration_actual / 60) if wave.duration_actual else None,
                'reversal_occurred': wave.reversal_occurred,
                
                # Metadata
                'wave_id': wave.id,
                'created_at': wave.created_at.isoformat(),
                'completed_at': wave.reached_at.isoformat() if wave.reached_at else None
            }
            
            training_data.append(wave_data)
        
        return training_data
    
    # Private helper methods
    
    def _get_market_data_for_range(
        self, 
        project_id: int, 
        start_time: datetime, 
        end_time: datetime,
        timeframe: str
    ) -> List[MarketData]:
        """Get market data for volume calculation range"""
        
        return self.db.query(MarketData).filter(
            and_(
                MarketData.project_id == project_id,
                MarketData.timeframe == timeframe,
                MarketData.timestamp >= start_time,
                MarketData.timestamp <= end_time
            )
        ).order_by(MarketData.timestamp).all()
    
    def _get_next_wave_number(self, project_id: int) -> int:
        """Get next sequential wave number for project"""
        
        last_wave = self.db.query(VolumeProfileWave).join(Annotation).filter(
            Annotation.project_id == project_id
        ).order_by(desc(VolumeProfileWave.wave_number)).first()
        
        return (last_wave.wave_number + 1) if last_wave and last_wave.wave_number else 1
    
    def _determine_confidence_level(self, quality_score: float) -> ConfidenceLevel:
        """Determine confidence level based on quality score"""
        
        if quality_score >= 0.8:
            return ConfidenceLevel.HIGH
        elif quality_score >= 0.6:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW
    
    def _calculate_wave_strength(self, validation_results: Dict) -> int:
        """Calculate wave strength (1-5 scale) based on validation"""
        
        quality_score = validation_results['metrics']['quality_score']
        poc_concentration = validation_results['metrics']['poc_concentration']
        
        # Combine quality score and POC concentration
        combined_score = (quality_score + poc_concentration) / 2
        
        if combined_score >= 0.8:
            return 5
        elif combined_score >= 0.6:
            return 4
        elif combined_score >= 0.4:
            return 3
        elif combined_score >= 0.2:
            return 2
        else:
            return 1
    
    def _get_volume_at_timestamp(self, market_data: List[MarketData], timestamp: datetime) -> Optional[Decimal]:
        """Get volume at specific timestamp"""
        
        for candle in market_data:
            if candle.timestamp == timestamp:
                return candle.volume
        
        return None
    
    def _estimate_poc_timestamp(self, market_data: List[MarketData], poc_price: Decimal) -> Optional[datetime]:
        """Estimate when POC price level was most active"""
        
        # Find candle with highest volume near POC price
        best_match = None
        min_distance = float('inf')
        
        for candle in market_data:
            # Check if POC price is within candle range
            if float(candle.low_price) <= float(poc_price) <= float(candle.high_price):
                # Prefer candle with higher volume if price matches
                if not best_match or candle.volume > best_match.volume:
                    best_match = candle
            else:
                # Find closest price
                candle_mid = (candle.high_price + candle.low_price) / 2
                distance = abs(float(candle_mid - poc_price))
                if distance < min_distance:
                    min_distance = distance
                    best_match = candle
        
        return best_match.timestamp if best_match else None
    
    def _update_wave_metrics(self, annotation_id: int, accuracy_percentage: float, duration_minutes: int):
        """Update pattern metrics with completion data"""
        
        # Update accuracy metric
        accuracy_metric = self.db.query(PatternMetrics).filter(
            and_(
                PatternMetrics.annotation_id == annotation_id,
                PatternMetrics.metric_name == 'target_accuracy_percentage'
            )
        ).first()
        
        if accuracy_metric:
            accuracy_metric.metric_value = Decimal(str(accuracy_percentage))
        
        # Update duration metric
        duration_metric = self.db.query(PatternMetrics).filter(
            and_(
                PatternMetrics.annotation_id == annotation_id,
                PatternMetrics.metric_name == 'wave_duration_minutes'
            )
        ).first()
        
        if duration_metric:
            duration_metric.metric_value = Decimal(str(duration_minutes))
    
    def _suggest_next_wave(
        self, 
        completed_wave: VolumeProfileWave, 
        reversal_price: Decimal, 
        reversal_time: datetime
    ):
        """Create suggestion for next wave after reversal"""
        
        # This is a placeholder for suggesting the next wave
        # In practice, you might want to notify the user or create a draft annotation
        
        suggestion = {
            'previous_wave_id': completed_wave.id,
            'suggested_start_point': {
                'price': reversal_price,
                'timestamp': reversal_time,
                'reason': f'Reversal from wave {completed_wave.wave_number} target'
            },
            'suggested_volume_range': {
                'start_time': reversal_time,
                # Suggest reasonable range based on previous wave
                'end_time': None  # User should specify
            }
        }
        
        # For now, just add to wave notes
        # In a full implementation, you might store this as a separate suggestion record
        completed_wave.notes = (completed_wave.notes or '') + f"\n\nNext wave suggestion: Start at {reversal_price} at {reversal_time}"