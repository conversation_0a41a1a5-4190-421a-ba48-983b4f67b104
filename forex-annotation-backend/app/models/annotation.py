from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, JSON, Enum, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base
import enum


class AnnotationType(str, enum.Enum):
    SIGNAL = "signal"
    PATTERN = "pattern"  
    NOTE = "note"
    VOLUME_PROFILE_WAVE = "volume_profile_wave"
    CUP_WITH_HANDLE = "cup_with_handle"
    VCP = "vcp"  # Volatility Contraction Pattern


class ValidationStatus(str, enum.Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    REJECTED = "rejected"
    NEEDS_REVIEW = "needs_review"


class Annotation(Base):
    __tablename__ = "annotations"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Association
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Core annotation data
    annotation_type = Column(Enum(AnnotationType), nullable=False, index=True)
    pattern_subtype = Column(String(100))  # For specific pattern variants
    
    # Time and price data
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    price_level = Column(Numeric(12, 6))
    
    # Annotation details (flexible JSON storage)
    data = Column(JSON, nullable=False)
    
    # Quality and validation
    confidence_level = Column(String(20), default="medium")  # low, medium, high
    quality_score = Column(Numeric(3, 2))  # 0.00 to 1.00
    validation_status = Column(Enum(ValidationStatus), default=ValidationStatus.PENDING)
    
    # Metadata
    notes = Column(Text)
    tags = Column(JSON)  # Array of string tags
    
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    project = relationship("Project", back_populates="annotations")
    user = relationship("User", back_populates="annotations") 
    pattern_metrics = relationship("PatternMetrics", back_populates="annotation")
    volume_wave = relationship("VolumeProfileWave", back_populates="annotation", uselist=False)
    
    def __repr__(self):
        return f"<Annotation(type='{self.annotation_type}', timestamp='{self.timestamp}')>"