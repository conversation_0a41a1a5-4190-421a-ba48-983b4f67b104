from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base


class PatternMetrics(Base):
    """Store specific metrics for different pattern types"""
    __tablename__ = "pattern_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    annotation_id = Column(Integer, ForeignKey("annotations.id"), nullable=False)
    
    # Metric details
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Numeric(10, 6), nullable=False)
    metric_type = Column(String(50), nullable=False)  # 'percentage', 'price', 'volume', 'time', 'ratio'
    metric_unit = Column(String(20))  # 'pips', 'minutes', 'lots', etc.
    
    # Additional metadata
    calculation_method = Column(String(100))
    confidence_score = Column(Numeric(3, 2))  # 0.00-1.00
    
    # For complex metrics that need structured data
    metric_details = Column(JSON)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    annotation = relationship("Annotation", back_populates="pattern_metrics")
    
    def __repr__(self):
        return f"<PatternMetrics(name='{self.metric_name}', value={self.metric_value})>"
    
    @classmethod
    def create_cup_with_handle_metrics(cls, annotation_id: int, cup_data: dict, handle_data: dict):
        """Factory method for Cup with Handle metrics"""
        metrics = []
        
        # Cup metrics
        metrics.extend([
            cls(
                annotation_id=annotation_id,
                metric_name="cup_depth_percentage", 
                metric_value=cup_data.get("depth_percentage"),
                metric_type="percentage",
                metric_unit="%"
            ),
            cls(
                annotation_id=annotation_id,
                metric_name="cup_width_days",
                metric_value=cup_data.get("width_days"), 
                metric_type="time",
                metric_unit="days"
            ),
            cls(
                annotation_id=annotation_id,
                metric_name="cup_symmetry_score",
                metric_value=cup_data.get("symmetry_score"),
                metric_type="ratio",
                metric_unit="score"
            )
        ])
        
        # Handle metrics
        metrics.extend([
            cls(
                annotation_id=annotation_id,
                metric_name="handle_depth_ratio",
                metric_value=handle_data.get("depth_ratio"),
                metric_type="ratio", 
                metric_unit="ratio"
            ),
            cls(
                annotation_id=annotation_id,
                metric_name="handle_width_days",
                metric_value=handle_data.get("width_days"),
                metric_type="time",
                metric_unit="days"
            )
        ])
        
        return metrics
    
    @classmethod  
    def create_vcp_metrics(cls, annotation_id: int, contractions: list):
        """Factory method for VCP metrics"""
        metrics = []
        
        for i, contraction in enumerate(contractions, 1):
            metrics.extend([
                cls(
                    annotation_id=annotation_id,
                    metric_name=f"contraction_{i}_percentage",
                    metric_value=contraction.get("percentage"),
                    metric_type="percentage",
                    metric_unit="%"
                ),
                cls(
                    annotation_id=annotation_id, 
                    metric_name=f"contraction_{i}_duration",
                    metric_value=contraction.get("duration_days"),
                    metric_type="time",
                    metric_unit="days"
                )
            ])
        
        # Overall VCP metrics
        metrics.append(
            cls(
                annotation_id=annotation_id,
                metric_name="vcp_stage_count",
                metric_value=len(contractions),
                metric_type="count",
                metric_unit="stages"
            )
        )
        
        return metrics
    
    @classmethod
    def create_volume_wave_metrics(cls, annotation_id: int, wave_data: dict):
        """Factory method for Volume Profile Wave metrics"""
        metrics = []
        
        metrics.extend([
            cls(
                annotation_id=annotation_id,
                metric_name="start_poc_distance_pips",
                metric_value=wave_data.get("start_poc_distance"),
                metric_type="price",
                metric_unit="pips"
            ),
            cls(
                annotation_id=annotation_id,
                metric_name="poc_volume_concentration",
                metric_value=wave_data.get("poc_concentration_percentage"),
                metric_type="percentage", 
                metric_unit="%"
            ),
            cls(
                annotation_id=annotation_id,
                metric_name="target_accuracy_percentage",
                metric_value=wave_data.get("accuracy_percentage"),
                metric_type="percentage",
                metric_unit="%"
            ),
            cls(
                annotation_id=annotation_id,
                metric_name="wave_duration_minutes", 
                metric_value=wave_data.get("duration_minutes"),
                metric_type="time",
                metric_unit="minutes"
            )
        ])
        
        return metrics