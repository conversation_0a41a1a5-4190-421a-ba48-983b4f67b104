from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, JSON, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base


class MarketData(Base):
    __tablename__ = "market_data"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Project association
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    
    # Market identification
    symbol = Column(String(20), nullable=False, index=True)  # EURUSD, GOLD, etc.
    timeframe = Column(String(10), nullable=False, index=True)  # 1m, 5m, 1h, 4h, 1d
    
    # OHLCV data
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    open_price = Column(Numeric(12, 6), nullable=False)
    high_price = Column(Numeric(12, 6), nullable=False)
    low_price = Column(Numeric(12, 6), nullable=False)
    close_price = Column(Numeric(12, 6), nullable=False)
    volume = Column(Numeric(15, 2), nullable=False)
    
    # Technical indicators (stored as JSON for flexibility)
    indicators = Column(JSON)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    project = relationship("Project", back_populates="market_data")
    
    # Composite indexes for performance
    __table_args__ = (
        Index('idx_symbol_timeframe_timestamp', 'symbol', 'timeframe', 'timestamp'),
        Index('idx_project_symbol_timeframe', 'project_id', 'symbol', 'timeframe'),
    )
    
    def __repr__(self):
        return f"<MarketData(symbol='{self.symbol}', timeframe='{self.timeframe}', timestamp='{self.timestamp}')>"
    
    @property
    def ohlc_tuple(self):
        """Return OHLC data as tuple for calculations"""
        return (self.open_price, self.high_price, self.low_price, self.close_price)
    
    @property
    def ohlcv_dict(self):
        """Return OHLCV data as dictionary"""
        return {
            'timestamp': self.timestamp,
            'open': float(self.open_price),
            'high': float(self.high_price), 
            'low': float(self.low_price),
            'close': float(self.close_price),
            'volume': float(self.volume)
        }