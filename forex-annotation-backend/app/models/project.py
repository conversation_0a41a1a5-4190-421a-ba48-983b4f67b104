from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base


class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text)
    
    # Configuration for this project
    config = Column(JSON)  # Store project-specific settings
    
    # Ownership
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    owner = relationship("User", back_populates="projects")
    market_data = relationship("MarketData", back_populates="project")
    annotations = relationship("Annotation", back_populates="project")
    
    def __repr__(self):
        return f"<Project(name='{self.name}', owner_id={self.user_id})>"