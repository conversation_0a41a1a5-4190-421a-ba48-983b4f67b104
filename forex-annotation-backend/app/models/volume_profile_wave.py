from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, Boolean, Text, JSON, Enum, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base
import enum


class WaveDirection(str, enum.Enum):
    BULLISH = "bullish"
    BEARISH = "bearish"


class ConfidenceLevel(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"


class ValidationStatus(str, enum.Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    REJECTED = "rejected"


class DistributionShape(str, enum.Enum):
    NORMAL = "normal"
    BIMODAL = "bimodal"
    SKEWED_HIGH = "skewed_high"
    SKEWED_LOW = "skewed_low"


class VolumeProfileWave(Base):
    """Core model for Volume Profile Wave Analysis"""
    __tablename__ = "volume_profile_waves"
    
    id = Column(Integer, primary_key=True, index=True)
    annotation_id = Column(Integer, ForeignKey("annotations.id"), nullable=False)
    wave_number = Column(Integer, index=True)
    
    # Wave characteristics
    wave_direction = Column(Enum(WaveDirection), nullable=False)
    wave_strength = Column(Integer)  # 1-5 scale
    
    # Core Components - Start Point
    start_point_price = Column(Numeric(12, 6), nullable=False)
    start_point_timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    start_point_volume = Column(Numeric(15, 2))
    selection_reason = Column(Text)
    
    # Core Components - Mode/POC (Point of Control)
    mode_poc_price = Column(Numeric(12, 6), nullable=False)
    mode_poc_timestamp = Column(DateTime(timezone=True))
    volume_density = Column(Numeric(15, 2))  # Volume at POC level
    total_volume_percentage = Column(Numeric(5, 2))  # POC volume as % of total
    
    # Core Components - Target Price
    calculated_target_price = Column(Numeric(12, 6), nullable=False)
    actual_reached_price = Column(Numeric(12, 6))
    accuracy_percentage = Column(Numeric(5, 2))
    reached_at = Column(DateTime(timezone=True))
    
    # Wave Analysis
    duration_actual = Column(Integer)  # Duration in minutes
    reversal_occurred = Column(Boolean, default=False)
    next_wave_id = Column(Integer, ForeignKey("volume_profile_waves.id"))
    
    # Quality Metrics
    prediction_accuracy = Column(Numeric(5, 2))  # 0-100%
    quality_score = Column(Numeric(3, 2))  # 0.00-1.00
    confidence_level = Column(Enum(ConfidenceLevel), default=ConfidenceLevel.MEDIUM)
    validation_status = Column(Enum(ValidationStatus), default=ValidationStatus.PENDING)
    
    # Metadata
    annotated_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    notes = Column(Text)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    annotation = relationship("Annotation", back_populates="volume_wave")
    annotated_by_user = relationship("User", back_populates="volume_waves")
    volume_distributions = relationship("VolumeDistribution", back_populates="wave")
    next_wave = relationship("VolumeProfileWave", remote_side=[id])
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_wave_timestamp', 'start_point_timestamp'),
        Index('idx_wave_user', 'annotated_by'),
        Index('idx_wave_status', 'validation_status'),
    )
    
    def __repr__(self):
        return f"<VolumeProfileWave(id={self.id}, direction='{self.wave_direction}', target={self.calculated_target_price})>"
    
    @property
    def target_formula_result(self) -> float:
        """Calculate target using formula: (Mode - Start) + Mode"""
        if self.mode_poc_price and self.start_point_price:
            distance = float(self.mode_poc_price - self.start_point_price)
            return float(self.mode_poc_price) + distance
        return None
    
    @property
    def is_completed(self) -> bool:
        """Check if wave has reached target or failed"""
        return self.actual_reached_price is not None


class VolumeDistribution(Base):
    """Volume distribution analysis for each wave"""
    __tablename__ = "volume_distributions"
    
    id = Column(Integer, primary_key=True, index=True)
    wave_id = Column(Integer, ForeignKey("volume_profile_waves.id"), nullable=False)
    
    # Calculation parameters
    timeframe = Column(String(10), nullable=False)
    calculation_start = Column(DateTime(timezone=True), nullable=False)
    calculation_end = Column(DateTime(timezone=True), nullable=False)
    
    # Volume Profile Statistics
    poc_price = Column(Numeric(12, 6), nullable=False)  # Point of Control
    value_area_high = Column(Numeric(12, 6))  # 70% volume area upper bound
    value_area_low = Column(Numeric(12, 6))   # 70% volume area lower bound
    total_volume = Column(Numeric(15, 2), nullable=False)
    price_range = Column(Numeric(12, 6))  # High - Low of period
    vwap = Column(Numeric(12, 6))  # Volume Weighted Average Price
    
    # Distribution characteristics
    distribution_shape = Column(Enum(DistributionShape))
    poc_dominance = Column(Numeric(5, 2))  # POC volume concentration %
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    wave = relationship("VolumeProfileWave", back_populates="volume_distributions")
    price_levels = relationship("VolumePriceLevel", back_populates="distribution")
    
    def __repr__(self):
        return f"<VolumeDistribution(wave_id={self.wave_id}, poc={self.poc_price})>"


class VolumePriceLevel(Base):
    """Individual price levels within volume distribution"""
    __tablename__ = "volume_price_levels"
    
    id = Column(Integer, primary_key=True, index=True)
    volume_distribution_id = Column(Integer, ForeignKey("volume_distributions.id"), nullable=False)
    
    # Price level data
    price_level = Column(Numeric(12, 6), nullable=False, index=True)
    volume_amount = Column(Numeric(15, 2), nullable=False)
    candle_count = Column(Integer)  # How many candles touched this price
    percentage_of_total = Column(Numeric(5, 2))  # % of total volume
    
    # Relationships
    distribution = relationship("VolumeDistribution", back_populates="price_levels")
    
    # Index for performance
    __table_args__ = (
        Index('idx_price_volume', 'volume_distribution_id', 'price_level'),
        Index('idx_volume_amount', 'volume_amount'),
    )
    
    def __repr__(self):
        return f"<VolumePriceLevel(price={self.price_level}, volume={self.volume_amount})>"