from fastapi import Fast<PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .core.config import settings
from .core.database import init_db
from .api import auth, projects, volume_waves, annotations


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    await init_db()
    yield
    # Shutdown


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.API_VERSION,
    description="Advanced FOREX/TFEX Data Annotation Tool with Volume Profile Wave Analysis",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(auth.router, prefix=f"/api/{settings.API_VERSION}/auth", tags=["Authentication"])
app.include_router(projects.router, prefix=f"/api/{settings.API_VERSION}/projects", tags=["Projects"])
app.include_router(volume_waves.router, prefix=f"/api/{settings.API_VERSION}/volume-waves", tags=["Volume Profile Waves"])
app.include_router(annotations.router, prefix=f"/api/{settings.API_VERSION}/annotations", tags=["Annotations"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "FOREX/TFEX Data Annotation Tool API",
        "version": settings.API_VERSION,
        "docs": "/docs",
        "features": [
            "Volume Profile Wave Analysis",
            "Cup with Handle Pattern Recognition", 
            "VCP (Volatility Contraction Pattern) Analysis",
            "Traditional Chart Pattern Annotation",
            "ML-Powered Start Point Prediction",
            "Real-time Collaboration",
            "Advanced Pattern Metrics"
        ]
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": settings.API_VERSION}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )