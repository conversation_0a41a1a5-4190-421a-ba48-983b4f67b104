# FOREX/TFEX Data Annotation Tool - Backend

## Overview

This is the backend API for the FOREX/TFEX Data Annotation Tool, featuring advanced Volume Profile Wave Analysis, Cup with Handle pattern recognition, VCP (Volatility Contraction Pattern) analysis, and traditional chart pattern annotation capabilities.

## Key Features

### Volume Profile Wave Analysis (Proprietary)
- **Start Point** identification and ML prediction
- **Mode/POC** (Point of Control) calculation with volume distribution analysis
- **Target Price** formula: `(Mode - Start) + Mode`
- Real-time wave tracking and accuracy measurement
- ML-powered start point prediction system

### Pattern Recognition
- Cup with Handle with geometric validation
- VCP (Volatility Contraction Pattern) with multi-stage tracking
- Traditional chart patterns (triangles, head & shoulders, etc.)
- Advanced pattern metrics and quality scoring

### Core Capabilities
- PostgreSQL database with optimized time-series data storage
- FastAPI with auto-generated OpenAPI documentation
- JWT-based authentication and user management
- Real-time WebSocket support for collaboration
- Comprehensive pattern validation and quality scoring
- ML feature extraction for algorithmic trading

## Quick Start

### Prerequisites
- Python 3.9+
- PostgreSQL 12+
- Redis (optional, for caching)

### Installation

1. **Clone and setup**
```bash
cd forex-annotation-backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Database setup**
```bash
# Create PostgreSQL database
createdb forex_annotation

# Copy environment file and configure
cp .env.example .env
# Edit .env with your database credentials

# Run migrations
alembic upgrade head
```

3. **Start the server**
```bash
# Development server
uvicorn app.main:app --reload

# Production server  
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

The API will be available at `http://localhost:8000`
- API Documentation: `http://localhost:8000/docs`
- Alternative docs: `http://localhost:8000/redoc`

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - Login and get JWT token
- `GET /api/v1/auth/me` - Get current user info

### Projects
- `GET /api/v1/projects` - List user projects
- `POST /api/v1/projects` - Create new project
- `GET /api/v1/projects/{id}` - Get project details
- `GET /api/v1/projects/{id}/stats` - Get project statistics

### Volume Profile Waves
- `POST /api/v1/volume-waves` - Create new wave annotation
- `GET /api/v1/volume-waves` - List waves for project
- `GET /api/v1/volume-waves/{id}` - Get wave with volume distribution
- `PUT /api/v1/volume-waves/{id}/completion` - Update wave completion
- `GET /api/v1/volume-waves/projects/{id}/success-rate` - Get success statistics
- `GET /api/v1/volume-waves/projects/{id}/training-data` - Export ML training data

### Annotations
- `POST /api/v1/annotations` - Create annotation
- `GET /api/v1/annotations` - List annotations
- `PUT /api/v1/annotations/{id}` - Update annotation
- `GET /api/v1/annotations/projects/{id}/summary` - Get project summary

## Volume Profile Wave Analysis

### The Three-Component System

1. **Start Point**
   - User-selected beginning of wave measurement
   - ML system learns to predict optimal start points
   - Based on volume surge, momentum, market structure

2. **Mode/POC (Point of Control)**
   - Price level with highest volume density in selected range
   - Automatically calculated from OHLCV data
   - Manual override available for edge cases

3. **Target Price**
   - Calculated using formula: `(Mode - Start) + Mode`
   - Example: Start=815, Mode=820 → Target=(820-815)+820=825
   - Real-time tracking of actual vs predicted results

### Wave Creation Process

```python
# Example wave creation
{
  "project_id": 1,
  "start_point": {
    "timestamp": "2024-01-15T10:30:00Z",
    "price": 1.0850
  },
  "volume_range": {
    "start_time": "2024-01-15T08:00:00Z", 
    "end_time": "2024-01-15T16:00:00Z"
  },
  "timeframe": "1h",
  "notes": "Strong volume surge at resistance break"
}
```

## Database Schema

### Core Tables
- `users` - User accounts and authentication
- `projects` - Project organization and settings
- `market_data` - OHLCV time-series data storage
- `annotations` - Base annotation records
- `volume_profile_waves` - Wave-specific data
- `volume_distributions` - Volume profile calculations
- `volume_price_levels` - Individual price/volume pairs
- `pattern_metrics` - Pattern-specific measurements

### Optimized for Performance
- Composite indexes on symbol/timeframe/timestamp
- Efficient volume profile queries
- Time-series data partitioning ready

## ML Feature Engineering

The system extracts 100+ features for start point prediction:

### Volume Features
- Volume surge ratios
- Volume concentration patterns  
- Historical wave volume analysis

### Price Action Features
- Momentum calculations
- Volatility expansion detection
- Breakout identification

### Market Structure Features
- Support/resistance proximity
- Fibonacci level analysis
- Previous wave context

### Training Data Export
```bash
GET /api/v1/volume-waves/projects/1/training-data
```

Returns structured data for ML model training with features and success labels.

## Configuration

### Environment Variables (.env)
```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/forex_annotation

# Security  
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API
DEBUG=True
CORS_ORIGINS=["http://localhost:3000"]

# Volume Profile Settings
DEFAULT_VOLUME_PROFILE_BINS=100
MIN_VOLUME_CALCULATION_PERIODS=10
MAX_VOLUME_CALCULATION_PERIODS=500
```

## Development

### Running Tests
```bash
pytest
```

### Database Migrations
```bash
# Create new migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

### Code Quality
```bash
# Format code
black .

# Lint code  
flake8 .

# Type checking
mypy .
```

## Production Deployment

### Docker (Recommended)
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Environment Setup
- Use PostgreSQL with connection pooling
- Configure Redis for session storage
- Set up proper logging and monitoring
- Use environment-specific .env files

## Architecture Notes

### Volume Profile Engine
- Efficient OHLCV volume distribution calculation
- POC identification with configurable precision
- Target price formula validation
- Quality scoring and validation

### Wave Annotation Service  
- Complete wave lifecycle management
- Real-time completion tracking
- Success rate analytics
- ML training data preparation

### Pattern Recognition
- Extensible pattern validation framework
- Quality scoring algorithms
- Historical success rate tracking
- Multi-pattern support (Cup & Handle, VCP, etc.)

This backend provides a solid foundation for advanced financial pattern recognition and algorithmic trading development.